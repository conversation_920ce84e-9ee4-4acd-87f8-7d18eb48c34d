"""
Advanced Aiming Methods
Implements multiple sophisticated aiming algorithms based on NeuralBot
Includes Flick, PID Feedback, and Hybrid aimers for Xbox controller
"""

import math
import time
import threading
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, Callable
import numpy as np


class AimingMethod(Enum):
    """Available aiming methods"""
    FLICK = "flick"
    PID_FEEDBACK = "pid_feedback"
    HYBRID = "hybrid"
    PREDICTIVE = "predictive"


@dataclass
class AimingConfig:
    """Configuration for aiming methods"""
    # General settings
    method: AimingMethod = AimingMethod.HYBRID
    max_speed: float = 1.0  # Maximum stick deflection (0.0 to 1.0)
    sensitivity: float = 1.0
    
    # Flick aimer settings
    flick_speed: float = 4000.0  # Pixels per second
    flick_smoothing: int = 10  # Smoothing steps
    trigger_delay_ms: int = 50
    cooldown_ms: int = 250
    continue_aiming: bool = False
    
    # PID settings
    pid_kp: float = 2.0  # Proportional gain
    pid_ki: float = 0.1  # Integral gain
    pid_kd: float = 0.5  # Derivative gain
    pid_max_integral: float = 100.0
    
    # Hybrid settings
    hybrid_switch_distance: float = 50.0  # Switch to flick when closer than this
    hybrid_p_gain: float = 1.5
    
    # Predictive settings
    prediction_time: float = 0.1  # Seconds to predict ahead
    velocity_smoothing: float = 0.3


class BaseAimer(ABC):
    """Base class for all aiming methods"""
    
    def __init__(self, config: AimingConfig, logger):
        self.config = config
        self.logger = logger
        self.last_target_pos = None
        self.last_time = 0.0
        self.active = False
    
    @abstractmethod
    def aim(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int, 
            dt: float, controller_callback: Callable[[float, float], None]) -> bool:
        """
        Perform aiming calculation
        
        Args:
            target_x, target_y: Target position
            screen_center_x, screen_center_y: Screen center
            dt: Delta time since last update
            controller_callback: Function to call with controller input (x, y) in range [-1, 1]
            
        Returns:
            True if aiming is complete/should trigger
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear aimer state"""
        pass
    
    def _calculate_controller_input(self, pixel_x: float, pixel_y: float, 
                                   max_distance: float = 200.0) -> Tuple[float, float]:
        """Convert pixel coordinates to controller input range [-1, 1]"""
        # Normalize to controller range
        controller_x = max(-1.0, min(1.0, pixel_x / max_distance))
        controller_y = max(-1.0, min(1.0, pixel_y / max_distance))
        
        # Apply sensitivity
        controller_x *= self.config.sensitivity
        controller_y *= self.config.sensitivity
        
        # Apply max speed limit
        magnitude = math.sqrt(controller_x**2 + controller_y**2)
        if magnitude > self.config.max_speed:
            controller_x = (controller_x / magnitude) * self.config.max_speed
            controller_y = (controller_y / magnitude) * self.config.max_speed
        
        return controller_x, controller_y


class FlickAimer(BaseAimer):
    """Flick aimer - smooth acquisition and precise flick to target"""
    
    def __init__(self, config: AimingConfig, logger):
        super().__init__(config, logger)
        self.flick_thread = None
        self.flick_active = False
        self.can_flick = True
    
    def aim(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int,
            dt: float, controller_callback: Callable[[float, float], None]) -> bool:
        
        if not self.can_flick or self.flick_active:
            return False
        
        # Calculate target offset
        offset_x = target_x - screen_center_x
        offset_y = target_y - screen_center_y
        
        # Start flick in separate thread
        if self.flick_thread is None or not self.flick_thread.is_alive():
            self.flick_thread = threading.Thread(
                target=self._execute_flick,
                args=(offset_x, offset_y, controller_callback),
                daemon=True
            )
            self.flick_thread.start()
            self.can_flick = not self.config.continue_aiming
        
        return False  # Flick aimer doesn't trigger immediately
    
    def _execute_flick(self, offset_x: float, offset_y: float, 
                      controller_callback: Callable[[float, float], None]) -> None:
        """Execute the flick movement in a separate thread"""
        self.flick_active = True
        
        try:
            # Calculate movement parameters
            distance = math.sqrt(offset_x**2 + offset_y**2)
            if distance == 0:
                return
            
            # Calculate time and frames for smooth movement
            time_to_target = distance / self.config.flick_speed
            frames = max(1, int(time_to_target * 1000 / self.config.flick_smoothing))
            
            # Execute smooth movement
            error_x = error_y = 0.0
            
            for i in range(frames):
                # Calculate movement for this frame
                target_x = error_x + offset_x / frames
                target_y = error_y + offset_y / frames
                
                # Convert to controller input
                controller_x, controller_y = self._calculate_controller_input(target_x, target_y)
                
                # Track error for next frame
                error_x = target_x - (controller_x * 200.0 / self.config.sensitivity)
                error_y = target_y - (controller_y * 200.0 / self.config.sensitivity)
                
                # Send controller input
                if abs(controller_x) > 0.01 or abs(controller_y) > 0.01:
                    controller_callback(controller_x, controller_y)
                
                # Wait for next frame
                if i < frames - 1:
                    time.sleep(self.config.flick_smoothing / 1000.0)
            
            # Trigger delay
            if self.config.trigger_delay_ms > 0:
                time.sleep(self.config.trigger_delay_ms / 1000.0)
            
            # Cooldown
            if self.config.cooldown_ms > 0:
                time.sleep(self.config.cooldown_ms / 1000.0)
                
        except Exception as e:
            self.logger.error(f"Flick execution failed: {e}")
        finally:
            self.flick_active = False
    
    def clear(self) -> None:
        """Clear flick aimer state"""
        self.can_flick = True
        self.flick_active = False


class PIDFeedbackAimer(BaseAimer):
    """PID feedback aimer - continuous tracking with control theory"""
    
    def __init__(self, config: AimingConfig, logger):
        super().__init__(config, logger)
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_error_x = 0.0
        self.last_error_y = 0.0
        self.last_time = 0.0
    
    def aim(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int,
            dt: float, controller_callback: Callable[[float, float], None]) -> bool:
        
        # Calculate error
        error_x = target_x - screen_center_x
        error_y = target_y - screen_center_y
        
        # PID calculations
        if dt > 0:
            # Proportional term
            p_x = self.config.pid_kp * error_x
            p_y = self.config.pid_kp * error_y
            
            # Integral term
            self.integral_x += error_x * dt
            self.integral_y += error_y * dt
            
            # Clamp integral to prevent windup
            self.integral_x = max(-self.config.pid_max_integral, 
                                min(self.config.pid_max_integral, self.integral_x))
            self.integral_y = max(-self.config.pid_max_integral, 
                                min(self.config.pid_max_integral, self.integral_y))
            
            i_x = self.config.pid_ki * self.integral_x
            i_y = self.config.pid_ki * self.integral_y
            
            # Derivative term
            d_x = self.config.pid_kd * (error_x - self.last_error_x) / dt
            d_y = self.config.pid_kd * (error_y - self.last_error_y) / dt
            
            # Combined PID output
            output_x = p_x + i_x + d_x
            output_y = p_y + i_y + d_y
            
            # Convert to controller input
            controller_x, controller_y = self._calculate_controller_input(output_x, output_y)
            
            # Send controller input
            if abs(controller_x) > 0.01 or abs(controller_y) > 0.01:
                controller_callback(controller_x, controller_y)
            
            # Update for next iteration
            self.last_error_x = error_x
            self.last_error_y = error_y
            
            # Check if we're close enough to trigger
            distance = math.sqrt(error_x**2 + error_y**2)
            return distance < 10.0  # Trigger when very close
        
        return False
    
    def clear(self) -> None:
        """Clear PID state"""
        self.integral_x = 0.0
        self.integral_y = 0.0
        self.last_error_x = 0.0
        self.last_error_y = 0.0


class HybridAimer(BaseAimer):
    """Hybrid aimer - combines PID feedback with flick finishing"""
    
    def __init__(self, config: AimingConfig, logger):
        super().__init__(config, logger)
        self.pid_aimer = PIDFeedbackAimer(config, logger)
        self.flick_aimer = FlickAimer(config, logger)
        self.in_flick_mode = False
    
    def aim(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int,
            dt: float, controller_callback: Callable[[float, float], None]) -> bool:
        
        # Calculate distance to target
        distance = math.sqrt((target_x - screen_center_x)**2 + (target_y - screen_center_y)**2)
        
        # Switch to flick mode when close
        if distance <= self.config.hybrid_switch_distance and not self.in_flick_mode:
            self.in_flick_mode = True
            self.pid_aimer.clear()
            return self.flick_aimer.aim(target_x, target_y, screen_center_x, screen_center_y, 
                                      dt, controller_callback)
        
        # Use PID for long-range tracking
        if not self.in_flick_mode:
            # Use proportional control for approach
            error_x = target_x - screen_center_x
            error_y = target_y - screen_center_y
            
            output_x = self.config.hybrid_p_gain * error_x
            output_y = self.config.hybrid_p_gain * error_y
            
            controller_x, controller_y = self._calculate_controller_input(output_x, output_y)
            
            if abs(controller_x) > 0.01 or abs(controller_y) > 0.01:
                controller_callback(controller_x, controller_y)
            
            return False
        else:
            # Continue with flick
            return self.flick_aimer.aim(target_x, target_y, screen_center_x, screen_center_y,
                                      dt, controller_callback)
    
    def clear(self) -> None:
        """Clear hybrid aimer state"""
        self.in_flick_mode = False
        self.pid_aimer.clear()
        self.flick_aimer.clear()


class PredictiveAimer(BaseAimer):
    """Predictive aimer - aims at predicted target position"""
    
    def __init__(self, config: AimingConfig, logger):
        super().__init__(config, logger)
        self.target_velocity_x = 0.0
        self.target_velocity_y = 0.0
        self.last_target_x = None
        self.last_target_y = None
        self.last_update_time = 0.0
    
    def aim(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int,
            dt: float, controller_callback: Callable[[float, float], None]) -> bool:
        
        current_time = time.time()
        
        # Calculate target velocity
        if self.last_target_x is not None and dt > 0:
            velocity_x = (target_x - self.last_target_x) / dt
            velocity_y = (target_y - self.last_target_y) / dt
            
            # Smooth velocity with exponential moving average
            alpha = self.config.velocity_smoothing
            self.target_velocity_x = alpha * velocity_x + (1 - alpha) * self.target_velocity_x
            self.target_velocity_y = alpha * velocity_y + (1 - alpha) * self.target_velocity_y
        
        # Predict future target position
        predicted_x = target_x + self.target_velocity_x * self.config.prediction_time
        predicted_y = target_y + self.target_velocity_y * self.config.prediction_time
        
        # Aim at predicted position
        error_x = predicted_x - screen_center_x
        error_y = predicted_y - screen_center_y
        
        # Use proportional control
        output_x = 2.0 * error_x
        output_y = 2.0 * error_y
        
        controller_x, controller_y = self._calculate_controller_input(output_x, output_y)
        
        if abs(controller_x) > 0.01 or abs(controller_y) > 0.01:
            controller_callback(controller_x, controller_y)
        
        # Update for next iteration
        self.last_target_x = target_x
        self.last_target_y = target_y
        self.last_update_time = current_time
        
        # Check if we're close to predicted position
        distance = math.sqrt(error_x**2 + error_y**2)
        return distance < 15.0
    
    def clear(self) -> None:
        """Clear predictive aimer state"""
        self.target_velocity_x = 0.0
        self.target_velocity_y = 0.0
        self.last_target_x = None
        self.last_target_y = None


class AdvancedAimingSystem:
    """Advanced aiming system that manages multiple aiming methods"""
    
    def __init__(self, config: AimingConfig, logger):
        self.config = config
        self.logger = logger
        
        # Initialize aimers
        self.aimers = {
            AimingMethod.FLICK: FlickAimer(config, logger),
            AimingMethod.PID_FEEDBACK: PIDFeedbackAimer(config, logger),
            AimingMethod.HYBRID: HybridAimer(config, logger),
            AimingMethod.PREDICTIVE: PredictiveAimer(config, logger)
        }
        
        self.current_aimer = self.aimers[config.method]
        self.logger.info(f"Advanced aiming system initialized with method: {config.method.value}")
    
    def set_aiming_method(self, method: AimingMethod) -> None:
        """Switch to a different aiming method"""
        if method in self.aimers:
            self.current_aimer.clear()
            self.current_aimer = self.aimers[method]
            self.config.method = method
            self.logger.info(f"Switched to aiming method: {method.value}")
    
    def aim_at_target(self, target_x: int, target_y: int, screen_center_x: int, 
                     screen_center_y: int, dt: float, 
                     controller_callback: Callable[[float, float], None]) -> bool:
        """
        Aim at target using the current aiming method
        
        Returns:
            True if ready to trigger/shoot
        """
        return self.current_aimer.aim(target_x, target_y, screen_center_x, screen_center_y,
                                    dt, controller_callback)
    
    def clear(self) -> None:
        """Clear all aimer states"""
        for aimer in self.aimers.values():
            aimer.clear()
    
    def get_aiming_info(self) -> dict:
        """Get information about current aiming state"""
        return {
            'method': self.config.method.value,
            'max_speed': self.config.max_speed,
            'sensitivity': self.config.sensitivity,
            'active_aimer': type(self.current_aimer).__name__
        }
