"""
Xbox Controller Input Module for AI Aimbot
Handles Xbox controller input via Bluetooth for console-style aiming
"""

import math
import time
import threading
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, Callable
import logging

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import xinput
    XINPUT_AVAILABLE = True
except ImportError:
    XINPUT_AVAILABLE = False

try:
    from inputs import get_gamepad, UnpluggedError
    INPUTS_AVAILABLE = True
except ImportError:
    INPUTS_AVAILABLE = False


class ControllerMethod(Enum):
    """Available controller input methods"""
    PYGAME = "pygame"
    XINPUT = "xinput"
    INPUTS = "inputs"


@dataclass
class ControllerState:
    """Current state of the Xbox controller"""
    left_stick_x: float = 0.0      # -1.0 to 1.0
    left_stick_y: float = 0.0      # -1.0 to 1.0
    right_stick_x: float = 0.0     # -1.0 to 1.0
    right_stick_y: float = 0.0     # -1.0 to 1.0
    left_trigger: float = 0.0      # 0.0 to 1.0
    right_trigger: float = 0.0     # 0.0 to 1.0
    a_button: bool = False
    b_button: bool = False
    x_button: bool = False
    y_button: bool = False
    left_bumper: bool = False
    right_bumper: bool = False
    back_button: bool = False
    start_button: bool = False
    left_stick_button: bool = False
    right_stick_button: bool = False
    dpad_up: bool = False
    dpad_down: bool = False
    dpad_left: bool = False
    dpad_right: bool = False
    connected: bool = False


@dataclass
class ControllerConfig:
    """Configuration for Xbox controller"""
    # Sensitivity settings
    aim_sensitivity: float = 1.0
    look_sensitivity: float = 1.5
    
    # Deadzone settings
    left_stick_deadzone: float = 0.15
    right_stick_deadzone: float = 0.10
    trigger_deadzone: float = 0.05
    
    # Acceleration settings
    enable_acceleration: bool = True
    acceleration_curve: float = 2.0  # Exponential curve for fine aiming
    
    # Aim assist settings
    aim_assist_strength: float = 0.8
    aim_assist_range: float = 100.0  # pixels
    
    # Button mappings
    aim_button: str = "left_trigger"      # Button to hold for aiming
    shoot_button: str = "right_trigger"   # Button to shoot
    toggle_button: str = "y_button"       # Button to toggle aimbot
    
    # Advanced settings
    invert_y_axis: bool = False
    response_curve: str = "dynamic"  # linear, dynamic, aggressive


class XboxControllerManager:
    """Manages Xbox controller input and state"""
    
    def __init__(self, config: ControllerConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # Controller state
        self.state = ControllerState()
        self.previous_state = ControllerState()
        
        # Threading
        self.running = False
        self.input_thread = None
        self.update_rate = 120  # Hz
        
        # Controller method
        self.method = self._detect_best_method()
        self.controller = None
        
        # Callbacks
        self.on_button_press: Optional[Callable[[str], None]] = None
        self.on_button_release: Optional[Callable[[str], None]] = None
        self.on_stick_move: Optional[Callable[[str, float, float], None]] = None
        
        self.logger.info(f"Xbox controller manager initialized with method: {self.method.value}")
    
    def _detect_best_method(self) -> ControllerMethod:
        """Detect the best available controller input method"""
        if XINPUT_AVAILABLE:
            return ControllerMethod.XINPUT
        elif PYGAME_AVAILABLE:
            return ControllerMethod.PYGAME
        elif INPUTS_AVAILABLE:
            return ControllerMethod.INPUTS
        else:
            raise RuntimeError("No controller input library available. Install pygame, xinput-python, or inputs")
    
    def initialize_controller(self) -> bool:
        """Initialize the controller based on the selected method"""
        try:
            if self.method == ControllerMethod.PYGAME:
                return self._init_pygame()
            elif self.method == ControllerMethod.XINPUT:
                return self._init_xinput()
            elif self.method == ControllerMethod.INPUTS:
                return self._init_inputs()
            return False
        except Exception as e:
            self.logger.error(f"Controller initialization failed: {e}")
            return False
    
    def _init_pygame(self) -> bool:
        """Initialize pygame controller"""
        pygame.init()
        pygame.joystick.init()
        
        if pygame.joystick.get_count() == 0:
            self.logger.error("No controllers detected")
            return False
        
        self.controller = pygame.joystick.Joystick(0)
        self.controller.init()
        
        controller_name = self.controller.get_name().lower()
        if "xbox" not in controller_name and "360" not in controller_name:
            self.logger.warning(f"Controller may not be Xbox compatible: {controller_name}")
        
        self.logger.info(f"Initialized controller: {self.controller.get_name()}")
        return True
    
    def _init_xinput(self) -> bool:
        """Initialize XInput controller"""
        # Check for connected controllers
        for i in range(4):  # XInput supports up to 4 controllers
            try:
                state = xinput.get_state(i)
                self.controller = i
                self.logger.info(f"Found Xbox controller at index {i}")
                return True
            except:
                continue
        
        self.logger.error("No Xbox controllers found via XInput")
        return False
    
    def _init_inputs(self) -> bool:
        """Initialize inputs library controller"""
        try:
            # Test if gamepad is available
            events = get_gamepad()
            self.controller = True  # inputs library doesn't need specific controller object
            self.logger.info("Initialized controller via inputs library")
            return True
        except UnpluggedError:
            self.logger.error("No controllers detected via inputs library")
            return False
    
    def start_input_loop(self) -> bool:
        """Start the controller input loop in a separate thread"""
        if not self.initialize_controller():
            return False
        
        self.running = True
        self.input_thread = threading.Thread(target=self._input_loop, daemon=True)
        self.input_thread.start()
        
        self.logger.info("Controller input loop started")
        return True
    
    def stop_input_loop(self):
        """Stop the controller input loop"""
        self.running = False
        if self.input_thread:
            self.input_thread.join(timeout=1.0)
        
        self.logger.info("Controller input loop stopped")
    
    def _input_loop(self):
        """Main controller input loop"""
        frame_time = 1.0 / self.update_rate
        
        while self.running:
            start_time = time.time()
            
            try:
                self.previous_state = ControllerState(**self.state.__dict__)
                
                if self.method == ControllerMethod.PYGAME:
                    self._update_pygame_state()
                elif self.method == ControllerMethod.XINPUT:
                    self._update_xinput_state()
                elif self.method == ControllerMethod.INPUTS:
                    self._update_inputs_state()
                
                self._process_state_changes()
                
            except Exception as e:
                self.logger.error(f"Controller input error: {e}")
                self.state.connected = False
            
            # Maintain update rate
            elapsed = time.time() - start_time
            sleep_time = frame_time - elapsed
            if sleep_time > 0:
                time.sleep(sleep_time)
    
    def _update_pygame_state(self):
        """Update controller state using pygame"""
        pygame.event.pump()
        
        if not self.controller:
            return
        
        # Analog sticks
        self.state.left_stick_x = self.controller.get_axis(0)
        self.state.left_stick_y = self.controller.get_axis(1)
        self.state.right_stick_x = self.controller.get_axis(2)
        self.state.right_stick_y = self.controller.get_axis(3)
        
        # Triggers (may vary by controller)
        try:
            self.state.left_trigger = max(0, self.controller.get_axis(4))
            self.state.right_trigger = max(0, self.controller.get_axis(5))
        except:
            # Fallback for controllers with different axis mapping
            pass
        
        # Buttons
        num_buttons = self.controller.get_numbuttons()
        if num_buttons >= 10:  # Standard Xbox controller has 10+ buttons
            self.state.a_button = self.controller.get_button(0)
            self.state.b_button = self.controller.get_button(1)
            self.state.x_button = self.controller.get_button(2)
            self.state.y_button = self.controller.get_button(3)
            self.state.left_bumper = self.controller.get_button(4)
            self.state.right_bumper = self.controller.get_button(5)
            self.state.back_button = self.controller.get_button(6)
            self.state.start_button = self.controller.get_button(7)
            self.state.left_stick_button = self.controller.get_button(8)
            self.state.right_stick_button = self.controller.get_button(9)
        
        # D-pad (hat)
        if self.controller.get_numhats() > 0:
            hat = self.controller.get_hat(0)
            self.state.dpad_left = hat[0] < 0
            self.state.dpad_right = hat[0] > 0
            self.state.dpad_down = hat[1] < 0
            self.state.dpad_up = hat[1] > 0
        
        self.state.connected = True
    
    def _update_xinput_state(self):
        """Update controller state using XInput"""
        try:
            state = xinput.get_state(self.controller)
            gamepad = state.gamepad
            
            # Normalize stick values from -32768 to 32767 range to -1.0 to 1.0
            self.state.left_stick_x = gamepad.l_thumb_x / 32767.0
            self.state.left_stick_y = gamepad.l_thumb_y / 32767.0
            self.state.right_stick_x = gamepad.r_thumb_x / 32767.0
            self.state.right_stick_y = gamepad.r_thumb_y / 32767.0
            
            # Normalize trigger values from 0 to 255 range to 0.0 to 1.0
            self.state.left_trigger = gamepad.l_trigger / 255.0
            self.state.right_trigger = gamepad.r_trigger / 255.0
            
            # Buttons
            buttons = gamepad.buttons
            self.state.a_button = bool(buttons & xinput.XUSB_GAMEPAD_A)
            self.state.b_button = bool(buttons & xinput.XUSB_GAMEPAD_B)
            self.state.x_button = bool(buttons & xinput.XUSB_GAMEPAD_X)
            self.state.y_button = bool(buttons & xinput.XUSB_GAMEPAD_Y)
            self.state.left_bumper = bool(buttons & xinput.XUSB_GAMEPAD_LEFT_SHOULDER)
            self.state.right_bumper = bool(buttons & xinput.XUSB_GAMEPAD_RIGHT_SHOULDER)
            self.state.back_button = bool(buttons & xinput.XUSB_GAMEPAD_BACK)
            self.state.start_button = bool(buttons & xinput.XUSB_GAMEPAD_START)
            self.state.left_stick_button = bool(buttons & xinput.XUSB_GAMEPAD_LEFT_THUMB)
            self.state.right_stick_button = bool(buttons & xinput.XUSB_GAMEPAD_RIGHT_THUMB)
            self.state.dpad_up = bool(buttons & xinput.XUSB_GAMEPAD_DPAD_UP)
            self.state.dpad_down = bool(buttons & xinput.XUSB_GAMEPAD_DPAD_DOWN)
            self.state.dpad_left = bool(buttons & xinput.XUSB_GAMEPAD_DPAD_LEFT)
            self.state.dpad_right = bool(buttons & xinput.XUSB_GAMEPAD_DPAD_RIGHT)
            
            self.state.connected = True
            
        except Exception as e:
            self.logger.error(f"XInput state update failed: {e}")
            self.state.connected = False
    
    def _update_inputs_state(self):
        """Update controller state using inputs library"""
        try:
            events = get_gamepad()
            for event in events:
                if event.ev_type == 'Absolute':
                    if event.code == 'ABS_X':  # Left stick X
                        self.state.left_stick_x = event.state / 32767.0
                    elif event.code == 'ABS_Y':  # Left stick Y
                        self.state.left_stick_y = event.state / 32767.0
                    elif event.code == 'ABS_RX':  # Right stick X
                        self.state.right_stick_x = event.state / 32767.0
                    elif event.code == 'ABS_RY':  # Right stick Y
                        self.state.right_stick_y = event.state / 32767.0
                    elif event.code == 'ABS_Z':  # Left trigger
                        self.state.left_trigger = event.state / 255.0
                    elif event.code == 'ABS_RZ':  # Right trigger
                        self.state.right_trigger = event.state / 255.0
                
                elif event.ev_type == 'Key':
                    button_pressed = event.state == 1
                    if event.code == 'BTN_A':
                        self.state.a_button = button_pressed
                    elif event.code == 'BTN_B':
                        self.state.b_button = button_pressed
                    elif event.code == 'BTN_X':
                        self.state.x_button = button_pressed
                    elif event.code == 'BTN_Y':
                        self.state.y_button = button_pressed
                    # Add more button mappings as needed
            
            self.state.connected = True
            
        except UnpluggedError:
            self.state.connected = False
        except Exception as e:
            self.logger.error(f"Inputs state update failed: {e}")
            self.state.connected = False
    
    def _process_state_changes(self):
        """Process state changes and trigger callbacks"""
        # Apply deadzones
        self.state.left_stick_x = self._apply_deadzone(self.state.left_stick_x, self.config.left_stick_deadzone)
        self.state.left_stick_y = self._apply_deadzone(self.state.left_stick_y, self.config.left_stick_deadzone)
        self.state.right_stick_x = self._apply_deadzone(self.state.right_stick_x, self.config.right_stick_deadzone)
        self.state.right_stick_y = self._apply_deadzone(self.state.right_stick_y, self.config.right_stick_deadzone)
        
        # Invert Y axis if configured
        if self.config.invert_y_axis:
            self.state.left_stick_y *= -1
            self.state.right_stick_y *= -1
        
        # Check for button press/release events
        self._check_button_events()
        
        # Check for stick movement
        if self.on_stick_move:
            if (abs(self.state.right_stick_x) > 0.01 or abs(self.state.right_stick_y) > 0.01):
                self.on_stick_move("right_stick", self.state.right_stick_x, self.state.right_stick_y)
    
    def _apply_deadzone(self, value: float, deadzone: float) -> float:
        """Apply deadzone to analog input"""
        if abs(value) < deadzone:
            return 0.0
        
        # Scale the remaining range
        sign = 1 if value > 0 else -1
        scaled = (abs(value) - deadzone) / (1.0 - deadzone)
        return sign * scaled
    
    def _check_button_events(self):
        """Check for button press/release events"""
        if not self.on_button_press and not self.on_button_release:
            return
        
        button_map = {
            'a_button': (self.state.a_button, self.previous_state.a_button),
            'b_button': (self.state.b_button, self.previous_state.b_button),
            'x_button': (self.state.x_button, self.previous_state.x_button),
            'y_button': (self.state.y_button, self.previous_state.y_button),
            'left_bumper': (self.state.left_bumper, self.previous_state.left_bumper),
            'right_bumper': (self.state.right_bumper, self.previous_state.right_bumper),
            'back_button': (self.state.back_button, self.previous_state.back_button),
            'start_button': (self.state.start_button, self.previous_state.start_button),
        }
        
        for button_name, (current, previous) in button_map.items():
            if current and not previous:  # Button pressed
                if self.on_button_press:
                    self.on_button_press(button_name)
            elif not current and previous:  # Button released
                if self.on_button_release:
                    self.on_button_release(button_name)
    
    def is_aiming(self) -> bool:
        """Check if the aim button is being held"""
        if self.config.aim_button == "left_trigger":
            return self.state.left_trigger > self.config.trigger_deadzone
        elif self.config.aim_button == "right_bumper":
            return self.state.right_bumper
        elif self.config.aim_button == "left_bumper":
            return self.state.left_bumper
        return False
    
    def is_shooting(self) -> bool:
        """Check if the shoot button is being held"""
        if self.config.shoot_button == "right_trigger":
            return self.state.right_trigger > self.config.trigger_deadzone
        elif self.config.shoot_button == "right_bumper":
            return self.state.right_bumper
        elif self.config.shoot_button == "a_button":
            return self.state.a_button
        return False
    
    def get_aim_input(self) -> Tuple[float, float]:
        """Get the current aim input from the right stick"""
        x = self.state.right_stick_x * self.config.aim_sensitivity
        y = self.state.right_stick_y * self.config.aim_sensitivity
        
        # Apply response curve
        if self.config.enable_acceleration:
            x = self._apply_response_curve(x)
            y = self._apply_response_curve(y)
        
        return x, y
    
    def _apply_response_curve(self, value: float) -> float:
        """Apply response curve for more precise aiming"""
        if abs(value) < 0.01:
            return 0.0
        
        sign = 1 if value > 0 else -1
        abs_value = abs(value)
        
        if self.config.response_curve == "linear":
            return value
        elif self.config.response_curve == "dynamic":
            # Slower for small movements, faster for large movements
            curved = pow(abs_value, self.config.acceleration_curve)
            return sign * curved
        elif self.config.response_curve == "aggressive":
            # Very sensitive for small movements
            curved = pow(abs_value, 0.5)
            return sign * curved
        
        return value
    
    def get_controller_info(self) -> dict:
        """Get information about the connected controller"""
        return {
            "method": self.method.value,
            "connected": self.state.connected,
            "controller_name": getattr(self.controller, 'get_name', lambda: 'Unknown')() if hasattr(self.controller, 'get_name') else 'Xbox Controller',
            "state": self.state
        }
