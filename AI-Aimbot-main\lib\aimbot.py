"""
Improved AI Aimbot - Lunar LITE Enhanced
Author: Enhanced by AI Assistant
Description: Refactored version with better code quality, error handling, and performance
"""

import ctypes
import cv2
import json
import logging
import math
import mss
import os
import random
import sys
import time
import threading
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import torch
import numpy as np
import win32api
from termcolor import colored
from ultralytics import YOLO


class MouseMethod(Enum):
    """Enumeration for mouse input methods"""
    WIN32 = "win32"
    DDXOFT = "ddxoft"


class AimbotState(Enum):
    """Enumeration for aimbot states"""
    DISABLED = "DISABLED"
    ENABLED = "ENABLED"
    PAUSED = "PAUSED"


@dataclass
class DetectionBox:
    """Data class for detection box coordinates"""
    left: int
    top: int
    width: int
    height: int


@dataclass
class Target:
    """Data class for detected target information"""
    x1: int
    y1: int
    x2: int
    y2: int
    head_x: int
    head_y: int
    confidence: float
    distance_to_crosshair: float


class InputMethod(Enum):
    """Enumeration for input methods"""
    MOUSE = "mouse"
    XBOX_CONTROLLER = "xbox_controller"


@dataclass
class AimbotConfig:
    """Configuration data class for aimbot settings"""
    # Detection settings
    fov: int = 350
    confidence_threshold: float = 0.45
    iou_threshold: float = 0.45
    aim_height_ratio: int = 10

    # Input settings
    input_method: InputMethod = InputMethod.XBOX_CONTROLLER
    mouse_method: MouseMethod = MouseMethod.DDXOFT
    mouse_delay: float = 0.0009
    pixel_increment: int = 1

    # Controller settings
    controller_sensitivity: float = 1.0
    controller_deadzone: float = 0.15
    controller_acceleration: bool = True

    # Behavior settings
    use_trigger_bot: bool = True
    target_lock_threshold: int = 5
    max_fps: int = 144

    # Randomization settings (anti-detection)
    enable_randomization: bool = True
    movement_randomness: float = 0.1
    timing_randomness: float = 0.05

    # Model settings
    model_path: str = "lib/best.pt"
    use_half_precision: bool = True

class Logger:
    """Enhanced logging system for the aimbot"""

    def __init__(self, name: str = "AimbotLogger", level: int = logging.INFO):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        if not self.logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)

            # File handler
            log_dir = Path("lib/logs")
            log_dir.mkdir(exist_ok=True)
            file_handler = logging.FileHandler(log_dir / "aimbot.log")
            file_handler.setLevel(logging.DEBUG)

            # Formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)

            self.logger.addHandler(console_handler)
            self.logger.addHandler(file_handler)

    def info(self, message: str) -> None:
        self.logger.info(message)

    def error(self, message: str) -> None:
        self.logger.error(message)

    def warning(self, message: str) -> None:
        self.logger.warning(message)

    def debug(self, message: str) -> None:
        self.logger.debug(message)


# Windows API structures for low-level input
PUL = ctypes.POINTER(ctypes.c_ulong)

class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

class POINT(ctypes.Structure):
    _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]


class ScreenCapture:
    """Handles screen capture operations"""

    def __init__(self, logger: Logger):
        self.logger = logger
        self.screen = mss.mss()
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        self.center_x = self.screen_width // 2
        self.center_y = self.screen_height // 2

    def get_detection_box(self, fov: int) -> DetectionBox:
        """Calculate detection box coordinates"""
        half_fov = fov // 2
        return DetectionBox(
            left=self.center_x - half_fov,
            top=self.center_y - half_fov,
            width=fov,
            height=fov
        )

    def capture_screen(self, detection_box: DetectionBox) -> Optional[np.ndarray]:
        """Capture screen region and return as numpy array"""
        try:
            monitor = {
                'left': detection_box.left,
                'top': detection_box.top,
                'width': detection_box.width,
                'height': detection_box.height
            }

            frame = self.screen.grab(monitor)
            frame_array = np.array(frame, dtype=np.uint8)

            if frame_array.size == 0:
                return None

            return cv2.cvtColor(frame_array, cv2.COLOR_BGRA2BGR)

        except Exception as e:
            self.logger.error(f"Screen capture failed: {e}")
            return None

    def cleanup(self) -> None:
        """Clean up screen capture resources"""
        try:
            self.screen.close()
        except Exception as e:
            self.logger.error(f"Screen cleanup failed: {e}")


class MouseController:
    """Handles mouse input operations"""

    def __init__(self, config: AimbotConfig, logger: Logger):
        self.config = config
        self.logger = logger
        self.mouse_dll = None
        self.extra = ctypes.c_ulong(0)
        self.ii_ = Input_I()

        self._initialize_mouse_method()

    def _initialize_mouse_method(self) -> None:
        """Initialize the selected mouse input method"""
        if self.config.mouse_method == MouseMethod.DDXOFT:
            try:
                dll_path = Path("lib/mouse/dd40605x64.dll").resolve()
                if not dll_path.exists():
                    raise FileNotFoundError(f"DDXoft DLL not found at {dll_path}")

                self.mouse_dll = ctypes.WinDLL(str(dll_path))
                time.sleep(0.1)  # Reduced initialization delay

                self.mouse_dll.DD_btn.argtypes = [ctypes.c_int]
                self.mouse_dll.DD_btn.restype = ctypes.c_int

                if self.mouse_dll.DD_btn(0) != 1:
                    raise RuntimeError("Failed to initialize DDXoft")

                self.logger.info("DDXoft mouse controller initialized successfully")

            except Exception as e:
                self.logger.warning(f"DDXoft initialization failed: {e}. Falling back to Win32")
                self.config.mouse_method = MouseMethod.WIN32

        if self.config.mouse_method == MouseMethod.WIN32:
            self.logger.info("Using Win32 mouse controller")

    def move_mouse(self, rel_x: int, rel_y: int) -> None:
        """Move mouse by relative coordinates"""
        try:
            if self.config.mouse_method == MouseMethod.DDXOFT and self.mouse_dll:
                self.mouse_dll.DD_movR(rel_x, rel_y)
            else:
                self.ii_.mi = MouseInput(rel_x, rel_y, 0, 0x0001, 0, ctypes.pointer(self.extra))
                input_obj = Input(ctypes.c_ulong(0), self.ii_)
                ctypes.windll.user32.SendInput(1, ctypes.byref(input_obj), ctypes.sizeof(input_obj))
        except Exception as e:
            self.logger.error(f"Mouse movement failed: {e}")

    def click(self) -> None:
        """Perform left mouse click"""
        try:
            if self.config.mouse_method == MouseMethod.DDXOFT and self.mouse_dll:
                self.mouse_dll.DD_btn(1)  # Mouse down
                time.sleep(0.001)
                self.mouse_dll.DD_btn(2)  # Mouse up
            else:
                ctypes.windll.user32.mouse_event(0x0002)  # Left mouse down
                time.sleep(0.0001)
                ctypes.windll.user32.mouse_event(0x0004)  # Left mouse up
        except Exception as e:
            self.logger.error(f"Mouse click failed: {e}")


class XboxControllerInterface:
    """Interface for Xbox controller input to the aimbot"""

    def __init__(self, config: AimbotConfig, logger: Logger):
        self.config = config
        self.logger = logger
        self.controller_manager = None
        self.last_aim_time = 0
        self.aim_accumulator_x = 0.0
        self.aim_accumulator_y = 0.0

        # Import Xbox controller module
        try:
            from xbox_controller import XboxControllerManager, ControllerConfig

            # Create controller configuration
            controller_config = ControllerConfig(
                aim_sensitivity=config.controller_sensitivity,
                right_stick_deadzone=config.controller_deadzone,
                enable_acceleration=config.controller_acceleration,
                aim_button="left_trigger",
                shoot_button="right_trigger"
            )

            self.controller_manager = XboxControllerManager(controller_config, logger)
            self.controller_manager.on_button_press = self._on_button_press

        except ImportError as e:
            self.logger.error(f"Xbox controller module not available: {e}")
            raise

    def initialize(self) -> bool:
        """Initialize the Xbox controller"""
        if not self.controller_manager:
            return False

        success = self.controller_manager.start_input_loop()
        if success:
            self.logger.info("Xbox controller initialized successfully")
        else:
            self.logger.error("Failed to initialize Xbox controller")

        return success

    def _on_button_press(self, button: str) -> None:
        """Handle button press events"""
        if button == "y_button":
            # Toggle aimbot (will be handled by main application)
            pass

    def is_aiming(self) -> bool:
        """Check if the player is aiming (left trigger pressed)"""
        if not self.controller_manager:
            return False
        return self.controller_manager.is_aiming()

    def is_shooting(self) -> bool:
        """Check if the player is shooting (right trigger pressed)"""
        if not self.controller_manager:
            return False
        return self.controller_manager.is_shooting()

    def apply_aim_assist(self, target_x: int, target_y: int, screen_center_x: int, screen_center_y: int) -> None:
        """Apply aim assist by simulating controller input"""
        if not self.controller_manager or not self.is_aiming():
            return

        try:
            # Calculate the difference between target and screen center
            diff_x = target_x - screen_center_x
            diff_y = target_y - screen_center_y

            # Convert pixel difference to controller input range (-1.0 to 1.0)
            # Scale based on screen size and sensitivity
            max_movement = 200.0  # Maximum pixel distance for full stick deflection

            controller_x = max(-1.0, min(1.0, diff_x / max_movement))
            controller_y = max(-1.0, min(1.0, diff_y / max_movement))

            # Apply sensitivity scaling
            controller_x *= self.config.controller_sensitivity
            controller_y *= self.config.controller_sensitivity

            # Apply smoothing and accumulation for more natural movement
            current_time = time.time()
            dt = current_time - self.last_aim_time
            self.last_aim_time = current_time

            # Smooth the input over time
            smoothing_factor = 0.3
            self.aim_accumulator_x = self.aim_accumulator_x * (1 - smoothing_factor) + controller_x * smoothing_factor
            self.aim_accumulator_y = self.aim_accumulator_y * (1 - smoothing_factor) + controller_y * smoothing_factor

            # Apply the aim assist by injecting controller input
            self._inject_controller_input(self.aim_accumulator_x, self.aim_accumulator_y)

        except Exception as e:
            self.logger.error(f"Aim assist application failed: {e}")

    def _inject_controller_input(self, x: float, y: float) -> None:
        """Inject controller input to simulate stick movement"""
        # This would require low-level controller input injection
        # For now, we'll use a simplified approach

        # Convert controller input back to screen movement for demonstration
        # In a real implementation, this would inject actual controller input
        movement_scale = 50.0  # Pixels per full stick deflection

        pixel_x = int(x * movement_scale)
        pixel_y = int(y * movement_scale)

        # For demonstration, we'll still use mouse movement
        # In production, this should inject actual controller input
        if abs(pixel_x) > 1 or abs(pixel_y) > 1:
            self.logger.debug(f"Aim assist: Controller input ({x:.2f}, {y:.2f}) -> Pixel movement ({pixel_x}, {pixel_y})")

    def get_controller_state(self) -> dict:
        """Get current controller state information"""
        if not self.controller_manager:
            return {"connected": False}

        return self.controller_manager.get_controller_info()

    def cleanup(self) -> None:
        """Clean up controller resources"""
        if self.controller_manager:
            self.controller_manager.stop_input_loop()
            self.logger.info("Xbox controller cleaned up")


class EnhancedAimbot:
    """Enhanced AI Aimbot with improved architecture and features"""

    def __init__(self, config: AimbotConfig, collect_data: bool = False):
        self.config = config
        self.collect_data = collect_data
        self.logger = Logger()
        self.state = AimbotState.ENABLED
        self.running = False

        # Initialize components
        self.screen_capture = ScreenCapture(self.logger)
        self.mouse_controller = MouseController(config, self.logger)

        # Load sensitivity configuration
        self.sensitivity_config = self._load_sensitivity_config()

        # Initialize AI model
        self.model = self._initialize_model()

        # Performance tracking
        self.frame_count = 0
        self.start_time = time.time()
        self.fps_limit = 1.0 / self.config.max_fps if self.config.max_fps > 0 else 0

        self.logger.info("Enhanced Aimbot initialized successfully")
        self.logger.info("Controls: F1 - Toggle, F2 - Quit")

    def _load_sensitivity_config(self) -> Dict:
        """Load sensitivity configuration from file"""
        try:
            config_path = Path("lib/config/config.json")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    return json.load(f)
            else:
                self.logger.warning("Sensitivity config not found, using defaults")
                return {"xy_sens": 1.0, "targeting_sens": 1.0, "xy_scale": 10.0, "targeting_scale": 1000.0}
        except Exception as e:
            self.logger.error(f"Failed to load sensitivity config: {e}")
            return {"xy_sens": 1.0, "targeting_sens": 1.0, "xy_scale": 10.0, "targeting_scale": 1000.0}

    def _initialize_model(self) -> YOLO:
        """Initialize YOLO model with error handling"""
        try:
            self.logger.info("Loading neural network model...")
            model_path = Path(self.config.model_path)

            if not model_path.exists():
                raise FileNotFoundError(f"Model file not found: {model_path}")

            model = YOLO(str(model_path))

            # Check CUDA availability
            if torch.cuda.is_available():
                self.logger.info(colored("CUDA ACCELERATION [ENABLED]", "green"))
            else:
                self.logger.warning(colored("CUDA ACCELERATION IS UNAVAILABLE", "red"))
                self.logger.warning("Performance may be poor without GPU acceleration")

            return model

        except Exception as e:
            self.logger.error(f"Failed to initialize model: {e}")
            raise

    def toggle_state(self) -> None:
        """Toggle aimbot state between enabled and disabled"""
        if self.state == AimbotState.ENABLED:
            self.state = AimbotState.DISABLED
            status_color = "red"
        else:
            self.state = AimbotState.ENABLED
            status_color = "green"

        status_text = colored(self.state.value, status_color)
        sys.stdout.write("\033[K")
        print(f"[!] AIMBOT IS [{status_text}]", end="\r")
        self.logger.info(f"Aimbot state changed to: {self.state.value}")

    @staticmethod
    def precise_sleep(duration: float) -> None:
        """High-precision sleep function"""
        if duration <= 0:
            return

        start_time = time.perf_counter()
        end_time = start_time + duration

        while time.perf_counter() < end_time:
            pass

    def is_enabled(self) -> bool:
        """Check if aimbot is enabled"""
        return self.state == AimbotState.ENABLED

    @staticmethod
    def is_shooting() -> bool:
        """Check if left mouse button is pressed"""
        try:
            return win32api.GetKeyState(0x01) in (-127, -128)
        except Exception:
            return False

    @staticmethod
    def is_targeting() -> bool:
        """Check if right mouse button is pressed (aiming)"""
        try:
            return win32api.GetKeyState(0x02) in (-127, -128)
        except Exception:
            return False

    def is_target_locked(self, x: int, y: int) -> bool:
        """Check if target is within lock threshold of crosshair"""
        threshold = self.config.target_lock_threshold
        center_x = self.screen_capture.center_x
        center_y = self.screen_capture.center_y

        return (center_x - threshold <= x <= center_x + threshold and
                center_y - threshold <= y <= center_y + threshold)

    def move_crosshair(self, target_x: int, target_y: int) -> None:
        """Move crosshair to target with smooth interpolation and randomization"""
        if not self.is_targeting():
            return

        try:
            scale = self.sensitivity_config.get("targeting_scale", 1000.0)

            for rel_x, rel_y in self._interpolate_movement(target_x, target_y, scale):
                # Add randomization for anti-detection
                if self.config.enable_randomization:
                    randomness = self.config.movement_randomness
                    rel_x += random.uniform(-randomness, randomness)
                    rel_y += random.uniform(-randomness, randomness)
                    rel_x, rel_y = int(rel_x), int(rel_y)

                self.mouse_controller.move_mouse(rel_x, rel_y)

                # Add timing randomization
                delay = self.config.mouse_delay
                if self.config.enable_randomization:
                    timing_variance = self.config.timing_randomness
                    delay += random.uniform(-timing_variance, timing_variance)
                    delay = max(0, delay)  # Ensure non-negative delay

                self.precise_sleep(delay)

        except Exception as e:
            self.logger.error(f"Crosshair movement failed: {e}")

    def _interpolate_movement(self, target_x: int, target_y: int, scale: float) -> List[Tuple[int, int]]:
        """Generate smooth movement path from current position to target"""
        center_x = self.screen_capture.center_x
        center_y = self.screen_capture.center_y

        diff_x = (target_x - center_x) * scale / self.config.pixel_increment
        diff_y = (target_y - center_y) * scale / self.config.pixel_increment

        distance = math.sqrt(diff_x * diff_x + diff_y * diff_y)

        if distance == 0:
            return []

        steps = max(1, int(distance))
        unit_x = diff_x / steps
        unit_y = diff_y / steps

        movements = []
        accumulated_x = accumulated_y = 0

        for step in range(steps):
            target_accumulated_x = unit_x * (step + 1)
            target_accumulated_y = unit_y * (step + 1)

            move_x = round(target_accumulated_x - accumulated_x)
            move_y = round(target_accumulated_y - accumulated_y)

            accumulated_x += move_x
            accumulated_y += move_y

            if move_x != 0 or move_y != 0:
                movements.append((move_x, move_y))

        return movements
            

    def _detect_targets(self, frame: np.ndarray) -> List[Target]:
        """Detect targets in the frame using YOLO model"""
        try:
            results = self.model.predict(
                source=frame,
                verbose=False,
                conf=self.config.confidence_threshold,
                iou=self.config.iou_threshold,
                half=self.config.use_half_precision
            )

            if not results or len(results[0].boxes.xyxy) == 0:
                return []

            targets = []
            fov_center = self.config.fov // 2

            for i, box in enumerate(results[0].boxes.xyxy):
                x1, y1, x2, y2 = map(int, box)

                # Calculate head position
                height = y2 - y1
                head_x = (x1 + x2) // 2
                head_y = int((y1 + y2) // 2 - height / self.config.aim_height_ratio)

                # Filter out own player (basic heuristic)
                if self._is_own_player(x1, y1, x2, y2):
                    continue

                # Calculate distance to crosshair
                distance = math.sqrt(
                    (head_x - fov_center) ** 2 + (head_y - fov_center) ** 2
                )

                # Get confidence score
                confidence = float(results[0].boxes.conf[i]) if len(results[0].boxes.conf) > i else 0.0

                targets.append(Target(
                    x1=x1, y1=y1, x2=x2, y2=y2,
                    head_x=head_x, head_y=head_y,
                    confidence=confidence,
                    distance_to_crosshair=distance
                ))

            # Sort by distance to crosshair (closest first)
            targets.sort(key=lambda t: t.distance_to_crosshair)
            return targets

        except Exception as e:
            self.logger.error(f"Target detection failed: {e}")
            return []

    def _is_own_player(self, x1: int, y1: int, x2: int, y2: int) -> bool:
        """Heuristic to detect if bounding box represents own player"""
        # Basic heuristics to avoid targeting own player
        return (x1 < 15 or
                (x1 < self.config.fov / 5 and y2 > self.config.fov / 1.2))

    def _draw_debug_info(self, frame: np.ndarray, targets: List[Target], fps: int) -> None:
        """Draw debug information on frame"""
        try:
            # Draw FPS
            cv2.putText(frame, f"FPS: {fps}", (5, 30),
                       cv2.FONT_HERSHEY_DUPLEX, 1, (113, 116, 244), 2)

            # Draw crosshair
            center = self.config.fov // 2
            cv2.circle(frame, (center, center), 3, (0, 255, 0), -1)

            # Draw target information
            for target in targets[:1]:  # Only draw closest target
                # Draw head circle
                cv2.circle(frame, (target.head_x, target.head_y), 5, (115, 244, 113), -1)

                # Draw line to target
                cv2.line(frame, (target.head_x, target.head_y), (center, center),
                        (244, 242, 113), 2)

                # Draw bounding box
                cv2.rectangle(frame, (target.x1, target.y1), (target.x2, target.y2),
                             (0, 255, 0), 2)

                # Draw status text
                absolute_head_x = target.head_x + self.screen_capture.get_detection_box(self.config.fov).left
                absolute_head_y = target.head_y + self.screen_capture.get_detection_box(self.config.fov).top

                if self.is_target_locked(absolute_head_x, absolute_head_y):
                    status_text = "LOCKED"
                    color = (115, 244, 113)
                else:
                    status_text = "TARGETING"
                    color = (115, 113, 244)

                cv2.putText(frame, status_text, (target.x1 + 40, target.y1),
                           cv2.FONT_HERSHEY_DUPLEX, 0.5, color, 2)

                # Draw confidence
                cv2.putText(frame, f"{target.confidence:.2f}", (target.x1, target.y1 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        except Exception as e:
            self.logger.error(f"Debug drawing failed: {e}")

    def start(self) -> None:
        """Start the aimbot main loop"""
        self.logger.info("Starting aimbot main loop")
        self.running = True
        self.toggle_state()  # Show initial status

        detection_box = self.screen_capture.get_detection_box(self.config.fov)
        last_frame_time = time.perf_counter()

        try:
            while self.running:
                frame_start = time.perf_counter()

                # Capture screen
                frame = self.screen_capture.capture_screen(detection_box)
                if frame is None:
                    continue

                # Detect targets
                targets = self._detect_targets(frame)

                # Process closest target
                if targets and self.is_enabled():
                    closest_target = targets[0]

                    # Calculate absolute coordinates
                    absolute_head_x = closest_target.head_x + detection_box.left
                    absolute_head_y = closest_target.head_y + detection_box.top

                    # Check if target is locked
                    if self.is_target_locked(absolute_head_x, absolute_head_y):
                        # Trigger bot functionality
                        if self.config.use_trigger_bot and not self.is_shooting():
                            self.mouse_controller.click()
                    else:
                        # Move crosshair to target
                        self.move_crosshair(absolute_head_x, absolute_head_y)

                # Calculate and display FPS
                frame_time = time.perf_counter() - frame_start
                fps = int(1 / frame_time) if frame_time > 0 else 0

                # Draw debug information
                self._draw_debug_info(frame, targets, fps)

                # Show frame
                cv2.imshow("Enhanced Aimbot - Lunar LITE", frame)

                # Handle window events
                key = cv2.waitKey(1) & 0xFF
                if key == ord('0') or key == 27:  # '0' or ESC to quit
                    break

                # Frame rate limiting
                if self.fps_limit > 0:
                    elapsed = time.perf_counter() - frame_start
                    sleep_time = self.fps_limit - elapsed
                    if sleep_time > 0:
                        time.sleep(sleep_time)

                # Update performance stats
                self.frame_count += 1

        except KeyboardInterrupt:
            self.logger.info("Aimbot stopped by user")
        except Exception as e:
            self.logger.error(f"Aimbot main loop error: {e}")
        finally:
            self.cleanup()

    def stop(self) -> None:
        """Stop the aimbot"""
        self.running = False
        self.logger.info("Aimbot stop requested")

    def cleanup(self) -> None:
        """Clean up resources"""
        try:
            self.logger.info("Cleaning up resources...")
            self.running = False

            # Close OpenCV windows
            cv2.destroyAllWindows()

            # Clean up screen capture
            self.screen_capture.cleanup()

            # Calculate final stats
            if self.frame_count > 0:
                runtime = time.time() - self.start_time
                avg_fps = self.frame_count / runtime if runtime > 0 else 0
                self.logger.info(f"Session stats - Frames: {self.frame_count}, "
                               f"Runtime: {runtime:.1f}s, Avg FPS: {avg_fps:.1f}")

            self.logger.info("Cleanup completed")

        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")


# Legacy compatibility functions
class Aimbot(EnhancedAimbot):
    """Legacy compatibility wrapper"""

    def __init__(self, box_constant=350, collect_data=False, mouse_delay=0.0009):
        config = AimbotConfig(
            fov=box_constant,
            mouse_delay=mouse_delay
        )
        super().__init__(config, collect_data)

        # Legacy attributes for compatibility
        self.box_constant = box_constant
        self.conf = config.confidence_threshold
        self.iou = config.iou_threshold
        self.collect_data = collect_data
        self.mouse_delay = mouse_delay
        self.mouse_method = config.mouse_method.value

    @staticmethod
    def update_status_aimbot():
        """Legacy method for toggling aimbot status"""
        # This will be handled by the keyboard listener in lunar.py
        pass

    @staticmethod
    def clean_up():
        """Legacy cleanup method"""
        print("\n[INFO] F2 WAS PRESSED. QUITTING...")
        os._exit(0)

if __name__ == "__main__":
    print("You are in the wrong directory and are running the wrong file; you must run lunar.py")
    print("For the enhanced version, use: python lunar.py --enhanced")
