"""
Anti-Detection Module for AI Aimbot
Implements various techniques to make the aimbot behavior more human-like
and harder to detect by anti-cheat systems
"""

import math
import random
import time
import numpy as np
from dataclasses import dataclass
from typing import List, Tuple, Optional
from enum import Enum


class MovementPattern(Enum):
    """Different movement patterns for humanization"""
    LINEAR = "linear"
    BEZIER = "bezier"
    NATURAL = "natural"
    JITTERY = "jittery"


@dataclass
class HumanBehaviorProfile:
    """Profile defining human-like behavior characteristics"""
    reaction_time_min: float = 0.15  # Minimum reaction time in seconds
    reaction_time_max: float = 0.35  # Maximum reaction time in seconds
    accuracy_variance: float = 0.1   # How much accuracy varies (0-1)
    movement_smoothness: float = 0.8  # How smooth movements are (0-1)
    fatigue_factor: float = 0.02     # How much performance degrades over time
    distraction_chance: float = 0.05  # Chance of missing obvious targets
    overcorrection_chance: float = 0.1  # Chance of overshooting target


class AntiDetectionSystem:
    """Advanced anti-detection system with human behavior simulation"""
    
    def __init__(self, logger, behavior_profile: Optional[HumanBehaviorProfile] = None):
        self.logger = logger
        self.profile = behavior_profile or HumanBehaviorProfile()
        
        # State tracking
        self.session_start_time = time.time()
        self.total_shots = 0
        self.total_hits = 0
        self.last_shot_time = 0
        self.consecutive_hits = 0
        self.fatigue_level = 0.0
        
        # Movement history for pattern analysis
        self.movement_history = []
        self.max_history_size = 100
        
        # Randomization seeds
        self.movement_seed = random.randint(1, 10000)
        self.timing_seed = random.randint(1, 10000)
        
        self.logger.info("Anti-detection system initialized")
    
    def should_engage_target(self, target_confidence: float, distance_to_crosshair: float) -> bool:
        """Determine if the aimbot should engage a target based on human-like decision making"""
        
        # Base engagement probability
        engagement_prob = target_confidence
        
        # Reduce probability for targets far from crosshair (human attention)
        distance_factor = max(0.1, 1.0 - (distance_to_crosshair / 200.0))
        engagement_prob *= distance_factor
        
        # Apply fatigue (performance degrades over time)
        session_time = time.time() - self.session_start_time
        fatigue_reduction = min(0.3, session_time * self.profile.fatigue_factor / 3600)  # Max 30% reduction per hour
        engagement_prob *= (1.0 - fatigue_reduction)
        
        # Random distraction (sometimes miss obvious targets)
        if random.random() < self.profile.distraction_chance:
            engagement_prob *= 0.3
        
        # Consecutive hit penalty (avoid looking too consistent)
        if self.consecutive_hits > 5:
            penalty = min(0.5, (self.consecutive_hits - 5) * 0.1)
            engagement_prob *= (1.0 - penalty)
        
        return random.random() < engagement_prob
    
    def calculate_reaction_delay(self) -> float:
        """Calculate human-like reaction delay"""
        base_delay = random.uniform(
            self.profile.reaction_time_min,
            self.profile.reaction_time_max
        )
        
        # Add fatigue effect
        fatigue_delay = self.fatigue_level * 0.1
        
        # Add some randomness based on recent performance
        if self.consecutive_hits > 3:
            # Slightly faster when "in the zone"
            base_delay *= 0.9
        elif self.consecutive_hits == 0 and self.total_shots > 5:
            # Slightly slower when missing
            base_delay *= 1.1
        
        return max(0.05, base_delay + fatigue_delay)
    
    def humanize_movement_path(self, start_pos: Tuple[int, int], end_pos: Tuple[int, int], 
                              pattern: MovementPattern = MovementPattern.NATURAL) -> List[Tuple[int, int]]:
        """Generate human-like movement path between two points"""
        
        if pattern == MovementPattern.LINEAR:
            return self._linear_path(start_pos, end_pos)
        elif pattern == MovementPattern.BEZIER:
            return self._bezier_path(start_pos, end_pos)
        elif pattern == MovementPattern.NATURAL:
            return self._natural_path(start_pos, end_pos)
        elif pattern == MovementPattern.JITTERY:
            return self._jittery_path(start_pos, end_pos)
        else:
            return self._natural_path(start_pos, end_pos)
    
    def _linear_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate linear movement path with slight randomization"""
        path = []
        distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
        steps = max(5, int(distance / 10))
        
        for i in range(steps + 1):
            t = i / steps
            x = int(start[0] + (end[0] - start[0]) * t)
            y = int(start[1] + (end[1] - start[1]) * t)
            
            # Add small random offset
            if i > 0 and i < steps:
                noise = self.profile.accuracy_variance * 2
                x += random.randint(-int(noise), int(noise))
                y += random.randint(-int(noise), int(noise))
            
            path.append((x, y))
        
        return path
    
    def _bezier_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate curved Bezier path"""
        # Create control points for natural curve
        mid_x = (start[0] + end[0]) / 2
        mid_y = (start[1] + end[1]) / 2
        
        # Add perpendicular offset for curve
        dx = end[0] - start[0]
        dy = end[1] - start[1]
        length = math.sqrt(dx*dx + dy*dy)
        
        if length > 0:
            # Perpendicular vector
            perp_x = -dy / length
            perp_y = dx / length
            
            # Random curve intensity
            curve_intensity = random.uniform(10, 30)
            control_x = mid_x + perp_x * curve_intensity
            control_y = mid_y + perp_y * curve_intensity
        else:
            control_x, control_y = mid_x, mid_y
        
        path = []
        steps = max(10, int(length / 8))
        
        for i in range(steps + 1):
            t = i / steps
            # Quadratic Bezier curve
            x = (1-t)**2 * start[0] + 2*(1-t)*t * control_x + t**2 * end[0]
            y = (1-t)**2 * start[1] + 2*(1-t)*t * control_y + t**2 * end[1]
            path.append((int(x), int(y)))
        
        return path
    
    def _natural_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate natural human-like movement path"""
        distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
        
        # Choose path type based on distance
        if distance < 50:
            return self._linear_path(start, end)
        elif distance < 150:
            # Slight curve with possible overshoot
            if random.random() < self.profile.overcorrection_chance:
                return self._overshoot_path(start, end)
            else:
                return self._bezier_path(start, end)
        else:
            # Longer movements might have multiple segments
            return self._segmented_path(start, end)
    
    def _jittery_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate jittery movement path (nervous/excited state)"""
        base_path = self._linear_path(start, end)
        jittery_path = []
        
        for i, (x, y) in enumerate(base_path):
            if i > 0 and i < len(base_path) - 1:
                # Add random jitter
                jitter_x = random.randint(-3, 3)
                jitter_y = random.randint(-3, 3)
                x += jitter_x
                y += jitter_y
            
            jittery_path.append((x, y))
        
        return jittery_path
    
    def _overshoot_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate path with overshoot and correction"""
        # Calculate overshoot point
        dx = end[0] - start[0]
        dy = end[1] - start[1]
        overshoot_factor = random.uniform(1.1, 1.3)
        
        overshoot_x = start[0] + dx * overshoot_factor
        overshoot_y = start[1] + dy * overshoot_factor
        
        # Path to overshoot point
        path1 = self._linear_path(start, (int(overshoot_x), int(overshoot_y)))
        # Correction path
        path2 = self._linear_path((int(overshoot_x), int(overshoot_y)), end)
        
        return path1 + path2[1:]  # Avoid duplicate point
    
    def _segmented_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Generate path with multiple segments (like human eye saccades)"""
        # Create intermediate points
        segments = random.randint(2, 4)
        points = [start]
        
        for i in range(1, segments):
            t = i / segments
            # Add some randomness to intermediate points
            base_x = start[0] + (end[0] - start[0]) * t
            base_y = start[1] + (end[1] - start[1]) * t
            
            offset = 20 * self.profile.accuracy_variance
            rand_x = base_x + random.uniform(-offset, offset)
            rand_y = base_y + random.uniform(-offset, offset)
            
            points.append((int(rand_x), int(rand_y)))
        
        points.append(end)
        
        # Connect all points
        full_path = []
        for i in range(len(points) - 1):
            segment = self._linear_path(points[i], points[i + 1])
            if i > 0:
                segment = segment[1:]  # Avoid duplicate points
            full_path.extend(segment)
        
        return full_path
    
    def add_timing_variance(self, base_delay: float) -> float:
        """Add human-like timing variance to delays"""
        # Use normal distribution for more realistic timing
        variance = base_delay * 0.2  # 20% variance
        delay = random.gauss(base_delay, variance)
        
        # Ensure minimum delay
        return max(0.001, delay)
    
    def update_performance_stats(self, hit: bool) -> None:
        """Update performance statistics for behavior adaptation"""
        self.total_shots += 1
        self.last_shot_time = time.time()
        
        if hit:
            self.total_hits += 1
            self.consecutive_hits += 1
        else:
            self.consecutive_hits = 0
        
        # Update fatigue level
        session_time = time.time() - self.session_start_time
        self.fatigue_level = min(1.0, session_time / 7200)  # Max fatigue after 2 hours
    
    def get_current_accuracy(self) -> float:
        """Get current accuracy percentage"""
        if self.total_shots == 0:
            return 0.0
        return (self.total_hits / self.total_shots) * 100
    
    def should_take_break(self) -> bool:
        """Determine if a human would take a break"""
        session_time = time.time() - self.session_start_time
        
        # Suggest break after extended play
        if session_time > 3600:  # 1 hour
            break_chance = min(0.3, (session_time - 3600) / 3600 * 0.1)
            return random.random() < break_chance
        
        return False
    
    def get_humanization_report(self) -> str:
        """Generate report on humanization effectiveness"""
        session_time = time.time() - self.session_start_time
        accuracy = self.get_current_accuracy()
        
        report = f"""
=== HUMANIZATION REPORT ===
Session Time: {session_time/60:.1f} minutes
Total Shots: {self.total_shots}
Accuracy: {accuracy:.1f}%
Consecutive Hits: {self.consecutive_hits}
Fatigue Level: {self.fatigue_level*100:.1f}%

Behavior Profile:
- Reaction Time: {self.profile.reaction_time_min:.2f}s - {self.profile.reaction_time_max:.2f}s
- Movement Smoothness: {self.profile.movement_smoothness*100:.1f}%
- Distraction Chance: {self.profile.distraction_chance*100:.1f}%

Status: {'Needs Break' if self.should_take_break() else 'Active'}
"""
        return report


class PatternDetectionAvoidance:
    """System to avoid creating detectable patterns"""
    
    def __init__(self, logger):
        self.logger = logger
        self.action_history = []
        self.timing_history = []
        self.max_history = 50
    
    def record_action(self, action_type: str, timestamp: float) -> None:
        """Record an action for pattern analysis"""
        self.action_history.append((action_type, timestamp))
        if len(self.action_history) > self.max_history:
            self.action_history.pop(0)
    
    def is_pattern_detected(self) -> bool:
        """Check if a detectable pattern exists in recent actions"""
        if len(self.action_history) < 10:
            return False
        
        # Check for timing patterns
        recent_timings = [t for _, t in self.action_history[-10:]]
        intervals = [recent_timings[i+1] - recent_timings[i] for i in range(len(recent_timings)-1)]
        
        # Check for too-regular intervals
        if len(intervals) > 5:
            avg_interval = sum(intervals) / len(intervals)
            variance = sum((x - avg_interval)**2 for x in intervals) / len(intervals)
            
            # If variance is too low, intervals are too regular
            if variance < (avg_interval * 0.1)**2:
                self.logger.warning("Regular timing pattern detected")
                return True
        
        return False
    
    def suggest_randomization(self) -> dict:
        """Suggest randomization parameters to break patterns"""
        return {
            "timing_variance": random.uniform(0.05, 0.15),
            "movement_variance": random.uniform(0.1, 0.3),
            "skip_chance": random.uniform(0.02, 0.08)
        }
