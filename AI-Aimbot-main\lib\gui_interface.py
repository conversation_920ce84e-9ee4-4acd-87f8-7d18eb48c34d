"""
GUI Interface for Enhanced AI Aimbot
Provides a user-friendly graphical interface for configuration and monitoring
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
from pathlib import Path
from typing import Optional, Callable

from config_manager import ConfigManager
from aimbot import <PERSON>mbotConfig, MouseMethod


class AimbotGUI:
    """Main GUI application for the aimbot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced Lunar AI Aimbot - Configuration")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Configuration manager
        self.config_manager = ConfigManager()
        
        # Current configurations
        self.aimbot_config = self.config_manager.load_aimbot_config()
        self.sensitivity_config = self.config_manager.load_sensitivity_config()
        
        # GUI variables
        self.setup_variables()
        
        # Create GUI
        self.create_widgets()
        
        # Load current values
        self.load_current_config()
        
        # Callbacks
        self.on_start_callback: Optional[Callable] = None
        self.on_stop_callback: Optional[Callable] = None
    
    def setup_variables(self):
        """Setup tkinter variables"""
        # Aimbot settings
        self.var_fov = tk.IntVar(value=self.aimbot_config.fov)
        self.var_confidence = tk.DoubleVar(value=self.aimbot_config.confidence_threshold)
        self.var_iou = tk.DoubleVar(value=self.aimbot_config.iou_threshold)
        self.var_aim_height = tk.IntVar(value=self.aimbot_config.aim_height_ratio)
        self.var_mouse_method = tk.StringVar(value=self.aimbot_config.mouse_method.value)
        self.var_mouse_delay = tk.DoubleVar(value=self.aimbot_config.mouse_delay)
        self.var_trigger_bot = tk.BooleanVar(value=self.aimbot_config.use_trigger_bot)
        self.var_max_fps = tk.IntVar(value=self.aimbot_config.max_fps)
        
        # Randomization settings
        self.var_enable_randomization = tk.BooleanVar(value=self.aimbot_config.enable_randomization)
        self.var_movement_randomness = tk.DoubleVar(value=self.aimbot_config.movement_randomness)
        self.var_timing_randomness = tk.DoubleVar(value=self.aimbot_config.timing_randomness)
        
        # Sensitivity settings
        self.var_xy_sens = tk.DoubleVar(value=self.sensitivity_config.get('xy_sens', 1.0))
        self.var_targeting_sens = tk.DoubleVar(value=self.sensitivity_config.get('targeting_sens', 1.0))
        
        # Status
        self.var_status = tk.StringVar(value="Ready")
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_basic_tab(notebook)
        self.create_advanced_tab(notebook)
        self.create_sensitivity_tab(notebook)
        self.create_profiles_tab(notebook)
        self.create_monitoring_tab(notebook)
        
        # Status bar
        self.create_status_bar()
        
        # Control buttons
        self.create_control_buttons()
    
    def create_basic_tab(self, notebook):
        """Create basic settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Basic Settings")
        
        # FOV setting
        ttk.Label(frame, text="Field of View (FOV):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=200, to=500, variable=self.var_fov, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_fov).grid(row=0, column=2, padx=5, pady=5)
        
        # Confidence threshold
        ttk.Label(frame, text="Confidence Threshold:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_confidence, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_confidence).grid(row=1, column=2, padx=5, pady=5)
        
        # Mouse method
        ttk.Label(frame, text="Mouse Method:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        mouse_combo = ttk.Combobox(frame, textvariable=self.var_mouse_method, values=['win32', 'ddxoft'], state='readonly')
        mouse_combo.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # Trigger bot
        ttk.Checkbutton(frame, text="Enable Trigger Bot", variable=self.var_trigger_bot).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Max FPS
        ttk.Label(frame, text="Max FPS (0 = unlimited):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0, to=240, variable=self.var_max_fps, orient=tk.HORIZONTAL).grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_max_fps).grid(row=4, column=2, padx=5, pady=5)
        
        # Configure column weights
        frame.columnconfigure(1, weight=1)
    
    def create_advanced_tab(self, notebook):
        """Create advanced settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Advanced Settings")
        
        # IOU threshold
        ttk.Label(frame, text="IOU Threshold:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_iou, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_iou).grid(row=0, column=2, padx=5, pady=5)
        
        # Aim height ratio
        ttk.Label(frame, text="Aim Height Ratio:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=2, to=20, variable=self.var_aim_height, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_aim_height).grid(row=1, column=2, padx=5, pady=5)
        
        # Mouse delay
        ttk.Label(frame, text="Mouse Delay (ms):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.0001, to=0.01, variable=self.var_mouse_delay, orient=tk.HORIZONTAL, resolution=0.0001).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_mouse_delay).grid(row=2, column=2, padx=5, pady=5)
        
        # Anti-detection settings
        ttk.Label(frame, text="Anti-Detection Settings", font=('TkDefaultFont', 10, 'bold')).grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15, 5))
        
        ttk.Checkbutton(frame, text="Enable Randomization", variable=self.var_enable_randomization).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(frame, text="Movement Randomness:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.0, to=0.5, variable=self.var_movement_randomness, orient=tk.HORIZONTAL, resolution=0.01).grid(row=5, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_movement_randomness).grid(row=5, column=2, padx=5, pady=5)
        
        ttk.Label(frame, text="Timing Randomness:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.0, to=0.2, variable=self.var_timing_randomness, orient=tk.HORIZONTAL, resolution=0.01).grid(row=6, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_timing_randomness).grid(row=6, column=2, padx=5, pady=5)
        
        frame.columnconfigure(1, weight=1)
    
    def create_sensitivity_tab(self, notebook):
        """Create sensitivity settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Sensitivity")
        
        # Instructions
        instructions = tk.Text(frame, height=4, wrap=tk.WORD)
        instructions.insert(tk.END, "Instructions:\n1. Set your in-game X and Y axis sensitivity to the same value\n2. Set your targeting sensitivity to match your scoping sensitivity\n3. Enter the exact values from your game settings below")
        instructions.config(state=tk.DISABLED)
        instructions.grid(row=0, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        # XY Sensitivity
        ttk.Label(frame, text="X/Y Axis Sensitivity:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_xy_sens, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Targeting Sensitivity
        ttk.Label(frame, text="Targeting Sensitivity:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_targeting_sens, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Calculate button
        ttk.Button(frame, text="Calculate Scales", command=self.calculate_sensitivity_scales).grid(row=3, column=0, columnspan=2, pady=10)
        
        # Display calculated values
        self.sensitivity_info = tk.Text(frame, height=6, wrap=tk.WORD)
        self.sensitivity_info.grid(row=4, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        frame.columnconfigure(2, weight=1)
    
    def create_profiles_tab(self, notebook):
        """Create profiles management tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Profiles")
        
        # Profile list
        ttk.Label(frame, text="Available Profiles:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.profile_listbox = tk.Listbox(frame, height=10)
        self.profile_listbox.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        
        # Profile buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Load Profile", command=self.load_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Profile", command=self.save_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete Profile", command=self.delete_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Refresh", command=self.refresh_profiles).pack(side=tk.LEFT, padx=5)
        
        # Load profiles
        self.refresh_profiles()
        
        frame.columnconfigure(1, weight=1)
    
    def create_monitoring_tab(self, notebook):
        """Create monitoring tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Monitoring")
        
        # Performance metrics
        self.metrics_text = tk.Text(frame, height=15, wrap=tk.WORD, font=('Courier', 10))
        self.metrics_text.grid(row=0, column=0, sticky=tk.NSEW, padx=5, pady=5)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)
        self.metrics_text.config(yscrollcommand=scrollbar.set)
        
        # Refresh button
        ttk.Button(frame, text="Refresh Metrics", command=self.refresh_metrics).grid(row=1, column=0, pady=5)
        
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Label(self.root, textvariable=self.var_status, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_control_buttons(self):
        """Create control buttons"""
        button_frame = ttk.Frame(self.root)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="Apply Settings", command=self.apply_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Aimbot", command=self.start_aimbot).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Stop Aimbot", command=self.stop_aimbot).pack(side=tk.RIGHT, padx=5)
    
    def load_current_config(self):
        """Load current configuration into GUI"""
        self.calculate_sensitivity_scales()
    
    def calculate_sensitivity_scales(self):
        """Calculate and display sensitivity scales"""
        try:
            xy_sens = self.var_xy_sens.get()
            targeting_sens = self.var_targeting_sens.get()
            
            if xy_sens <= 0 or targeting_sens <= 0:
                raise ValueError("Sensitivity values must be positive")
            
            xy_scale = 10.0 / xy_sens
            targeting_scale = 1000.0 / (targeting_sens * xy_sens)
            
            info_text = f"""Calculated Sensitivity Scales:
XY Scale: {xy_scale:.4f}
Targeting Scale: {targeting_scale:.4f}

These values will be used for mouse movement calculations.
Higher scales = more sensitive movement.
Lower scales = less sensitive movement.
"""
            
            self.sensitivity_info.config(state=tk.NORMAL)
            self.sensitivity_info.delete(1.0, tk.END)
            self.sensitivity_info.insert(tk.END, info_text)
            self.sensitivity_info.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate sensitivity scales: {e}")
    
    def apply_settings(self):
        """Apply current settings"""
        try:
            # Update aimbot config
            self.aimbot_config.fov = self.var_fov.get()
            self.aimbot_config.confidence_threshold = self.var_confidence.get()
            self.aimbot_config.iou_threshold = self.var_iou.get()
            self.aimbot_config.aim_height_ratio = self.var_aim_height.get()
            self.aimbot_config.mouse_method = MouseMethod(self.var_mouse_method.get())
            self.aimbot_config.mouse_delay = self.var_mouse_delay.get()
            self.aimbot_config.use_trigger_bot = self.var_trigger_bot.get()
            self.aimbot_config.max_fps = self.var_max_fps.get()
            self.aimbot_config.enable_randomization = self.var_enable_randomization.get()
            self.aimbot_config.movement_randomness = self.var_movement_randomness.get()
            self.aimbot_config.timing_randomness = self.var_timing_randomness.get()
            
            # Save configurations
            self.config_manager.save_aimbot_config(self.aimbot_config)
            self.config_manager.save_sensitivity_config(
                self.var_xy_sens.get(),
                self.var_targeting_sens.get()
            )
            
            self.var_status.set("Settings applied successfully")
            messagebox.showinfo("Success", "Settings applied and saved successfully!")
            
        except Exception as e:
            self.var_status.set(f"Error: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Confirm Reset", "Reset all settings to defaults?"):
            default_config = AimbotConfig()
            
            self.var_fov.set(default_config.fov)
            self.var_confidence.set(default_config.confidence_threshold)
            self.var_iou.set(default_config.iou_threshold)
            self.var_aim_height.set(default_config.aim_height_ratio)
            self.var_mouse_method.set(default_config.mouse_method.value)
            self.var_mouse_delay.set(default_config.mouse_delay)
            self.var_trigger_bot.set(default_config.use_trigger_bot)
            self.var_max_fps.set(default_config.max_fps)
            self.var_enable_randomization.set(default_config.enable_randomization)
            self.var_movement_randomness.set(default_config.movement_randomness)
            self.var_timing_randomness.set(default_config.timing_randomness)
            
            self.var_xy_sens.set(1.0)
            self.var_targeting_sens.set(1.0)
            
            self.calculate_sensitivity_scales()
            self.var_status.set("Settings reset to defaults")
    
    def refresh_profiles(self):
        """Refresh the profiles list"""
        self.profile_listbox.delete(0, tk.END)
        profiles = self.config_manager.list_profiles()
        for profile in profiles:
            self.profile_listbox.insert(tk.END, profile)
    
    def load_profile(self):
        """Load selected profile"""
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to load")
            return
        
        profile_name = self.profile_listbox.get(selection[0])
        result = self.config_manager.load_profile(profile_name)
        
        if result:
            config, sensitivity = result
            # Update GUI variables
            self.var_fov.set(config.fov)
            self.var_confidence.set(config.confidence_threshold)
            # ... (update all other variables)
            
            self.var_status.set(f"Profile '{profile_name}' loaded")
            messagebox.showinfo("Success", f"Profile '{profile_name}' loaded successfully!")
        else:
            messagebox.showerror("Error", f"Failed to load profile '{profile_name}'")
    
    def save_profile(self):
        """Save current settings as a profile"""
        profile_name = tk.simpledialog.askstring("Save Profile", "Enter profile name:")
        if not profile_name:
            return
        
        try:
            # Apply current settings first
            self.apply_settings()
            
            # Save as profile
            self.config_manager.save_profile(
                profile_name,
                self.aimbot_config,
                self.config_manager.load_sensitivity_config()
            )
            
            self.refresh_profiles()
            self.var_status.set(f"Profile '{profile_name}' saved")
            messagebox.showinfo("Success", f"Profile '{profile_name}' saved successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save profile: {e}")
    
    def delete_profile(self):
        """Delete selected profile"""
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to delete")
            return
        
        profile_name = self.profile_listbox.get(selection[0])
        
        if messagebox.askyesno("Confirm Delete", f"Delete profile '{profile_name}'?"):
            if self.config_manager.delete_profile(profile_name):
                self.refresh_profiles()
                self.var_status.set(f"Profile '{profile_name}' deleted")
                messagebox.showinfo("Success", f"Profile '{profile_name}' deleted successfully!")
            else:
                messagebox.showerror("Error", f"Failed to delete profile '{profile_name}'")
    
    def refresh_metrics(self):
        """Refresh performance metrics display"""
        # This would be connected to the actual aimbot performance data
        metrics_text = """Performance Metrics:
FPS: 60.0
Frame Time: 16.7ms
Detection Time: 5.2ms
Movement Time: 1.1ms
Memory Usage: 245.6MB
CPU Usage: 15.2%
GPU Usage: 45.8%

Status: Running
Targets Detected: 1,234
Shots Fired: 567
Accuracy: 78.5%
"""
        
        self.metrics_text.config(state=tk.NORMAL)
        self.metrics_text.delete(1.0, tk.END)
        self.metrics_text.insert(tk.END, metrics_text)
        self.metrics_text.config(state=tk.DISABLED)
    
    def start_aimbot(self):
        """Start the aimbot"""
        if self.on_start_callback:
            self.on_start_callback()
        self.var_status.set("Aimbot started")
    
    def stop_aimbot(self):
        """Stop the aimbot"""
        if self.on_stop_callback:
            self.on_stop_callback()
        self.var_status.set("Aimbot stopped")
    
    def run(self):
        """Run the GUI application"""
        self.root.mainloop()


if __name__ == "__main__":
    app = AimbotGUI()
    app.run()
