"""
Performance Optimization Module for AI Aimbot
Provides various optimizations to improve FPS and reduce latency
"""

import gc
import os
import psutil
import threading
import time
from dataclasses import dataclass
from typing import Optional, Callable
import numpy as np
import torch


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    fps: float = 0.0
    avg_fps: float = 0.0
    frame_time: float = 0.0
    detection_time: float = 0.0
    movement_time: float = 0.0
    memory_usage: float = 0.0
    gpu_usage: float = 0.0
    cpu_usage: float = 0.0


class PerformanceOptimizer:
    """Performance optimization and monitoring system"""
    
    def __init__(self, logger):
        self.logger = logger
        self.metrics = PerformanceMetrics()
        self.frame_times = []
        self.max_frame_history = 100
        self.optimization_enabled = True
        
        # Performance settings
        self.gc_interval = 100  # Frames between garbage collection
        self.frame_counter = 0
        
        # Threading
        self.monitor_thread = None
        self.monitoring = False
        
        self._apply_system_optimizations()
    
    def _apply_system_optimizations(self):
        """Apply system-level optimizations"""
        try:
            # Set process priority (Windows)
            if os.name == 'nt':
                import win32process
                import win32con
                handle = win32process.GetCurrentProcess()
                win32process.SetPriorityClass(handle, win32con.HIGH_PRIORITY_CLASS)
                self.logger.info("Set process priority to HIGH")
            
            # PyTorch optimizations
            if torch.cuda.is_available():
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                self.logger.info("Enabled CUDNN optimizations")
            
            # Garbage collection optimization
            gc.set_threshold(700, 10, 10)  # More aggressive GC
            
        except Exception as e:
            self.logger.warning(f"Some system optimizations failed: {e}")
    
    def start_monitoring(self):
        """Start performance monitoring in background thread"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.logger.info("Performance monitoring stopped")
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring:
            try:
                # Update system metrics
                process = psutil.Process()
                self.metrics.memory_usage = process.memory_info().rss / 1024 / 1024  # MB
                self.metrics.cpu_usage = process.cpu_percent()
                
                # GPU usage (if available)
                if torch.cuda.is_available():
                    self.metrics.gpu_usage = torch.cuda.utilization()
                
                time.sleep(1.0)  # Update every second
                
            except Exception as e:
                self.logger.error(f"Monitoring error: {e}")
                break
    
    def update_frame_metrics(self, frame_time: float, detection_time: float = 0.0, movement_time: float = 0.0):
        """Update frame timing metrics"""
        self.frame_counter += 1
        
        # Update current metrics
        self.metrics.frame_time = frame_time
        self.metrics.detection_time = detection_time
        self.metrics.movement_time = movement_time
        self.metrics.fps = 1.0 / frame_time if frame_time > 0 else 0.0
        
        # Track frame times for average calculation
        self.frame_times.append(frame_time)
        if len(self.frame_times) > self.max_frame_history:
            self.frame_times.pop(0)
        
        # Calculate average FPS
        if self.frame_times:
            avg_frame_time = sum(self.frame_times) / len(self.frame_times)
            self.metrics.avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0.0
        
        # Periodic optimizations
        if self.optimization_enabled and self.frame_counter % self.gc_interval == 0:
            self._periodic_optimization()
    
    def _periodic_optimization(self):
        """Perform periodic optimizations"""
        try:
            # Garbage collection
            collected = gc.collect()
            if collected > 0:
                self.logger.debug(f"Garbage collected {collected} objects")
            
            # CUDA cache cleanup
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
        except Exception as e:
            self.logger.error(f"Periodic optimization failed: {e}")
    
    def get_performance_report(self) -> str:
        """Generate performance report"""
        report = f"""
=== PERFORMANCE REPORT ===
Current FPS: {self.metrics.fps:.1f}
Average FPS: {self.metrics.avg_fps:.1f}
Frame Time: {self.metrics.frame_time*1000:.2f}ms
Detection Time: {self.metrics.detection_time*1000:.2f}ms
Movement Time: {self.metrics.movement_time*1000:.2f}ms
Memory Usage: {self.metrics.memory_usage:.1f}MB
CPU Usage: {self.metrics.cpu_usage:.1f}%
GPU Usage: {self.metrics.gpu_usage:.1f}%
Total Frames: {self.frame_counter}
"""
        return report
    
    def optimize_model_inference(self, model):
        """Apply model-specific optimizations"""
        try:
            # Enable model optimizations
            model.model.eval()  # Ensure eval mode
            
            # Compile model if using PyTorch 2.0+
            if hasattr(torch, 'compile'):
                try:
                    model.model = torch.compile(model.model, mode='max-autotune')
                    self.logger.info("Model compiled with torch.compile")
                except Exception as e:
                    self.logger.warning(f"Model compilation failed: {e}")
            
            # Set optimal inference settings
            if torch.cuda.is_available():
                # Use mixed precision
                model.model.half()  # Convert to FP16
                self.logger.info("Enabled FP16 inference")
            
        except Exception as e:
            self.logger.error(f"Model optimization failed: {e}")
    
    def get_optimal_batch_size(self, input_shape: tuple) -> int:
        """Calculate optimal batch size for inference"""
        if not torch.cuda.is_available():
            return 1
        
        try:
            # Get GPU memory info
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            available_memory = gpu_memory - torch.cuda.memory_allocated()
            
            # Estimate memory per sample (rough calculation)
            sample_memory = np.prod(input_shape) * 4  # 4 bytes per float32
            
            # Use 80% of available memory
            max_batch_size = int((available_memory * 0.8) / sample_memory)
            
            # Clamp to reasonable range
            return max(1, min(max_batch_size, 8))
            
        except Exception as e:
            self.logger.error(f"Batch size calculation failed: {e}")
            return 1
    
    def create_frame_buffer(self, size: int = 3):
        """Create optimized frame buffer for smooth processing"""
        return FrameBuffer(size, self.logger)


class FrameBuffer:
    """Optimized frame buffer for smooth video processing"""
    
    def __init__(self, size: int, logger):
        self.size = size
        self.logger = logger
        self.buffer = []
        self.lock = threading.Lock()
        self.current_index = 0
    
    def add_frame(self, frame: np.ndarray) -> bool:
        """Add frame to buffer"""
        try:
            with self.lock:
                if len(self.buffer) >= self.size:
                    # Replace oldest frame
                    self.buffer[self.current_index] = frame.copy()
                    self.current_index = (self.current_index + 1) % self.size
                else:
                    self.buffer.append(frame.copy())
                return True
        except Exception as e:
            self.logger.error(f"Frame buffer add failed: {e}")
            return False
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """Get the most recent frame"""
        try:
            with self.lock:
                if not self.buffer:
                    return None
                
                latest_index = (self.current_index - 1) % len(self.buffer)
                return self.buffer[latest_index].copy()
        except Exception as e:
            self.logger.error(f"Frame buffer get failed: {e}")
            return None
    
    def get_average_frame(self) -> Optional[np.ndarray]:
        """Get averaged frame for noise reduction"""
        try:
            with self.lock:
                if not self.buffer:
                    return None
                
                # Average all frames in buffer
                avg_frame = np.mean(self.buffer, axis=0).astype(np.uint8)
                return avg_frame
        except Exception as e:
            self.logger.error(f"Frame averaging failed: {e}")
            return None
    
    def clear(self):
        """Clear the buffer"""
        with self.lock:
            self.buffer.clear()
            self.current_index = 0


class AdaptiveQualityController:
    """Adaptive quality controller to maintain target FPS"""
    
    def __init__(self, target_fps: float = 60.0, logger=None):
        self.target_fps = target_fps
        self.target_frame_time = 1.0 / target_fps
        self.logger = logger
        
        # Quality settings
        self.current_quality = 1.0  # 1.0 = full quality, 0.5 = half quality
        self.min_quality = 0.3
        self.max_quality = 1.0
        
        # Adaptation parameters
        self.adaptation_rate = 0.1
        self.frame_time_history = []
        self.history_size = 10
    
    def update(self, frame_time: float) -> float:
        """Update quality based on performance"""
        self.frame_time_history.append(frame_time)
        if len(self.frame_time_history) > self.history_size:
            self.frame_time_history.pop(0)
        
        # Calculate average frame time
        avg_frame_time = sum(self.frame_time_history) / len(self.frame_time_history)
        
        # Adjust quality based on performance
        if avg_frame_time > self.target_frame_time * 1.2:  # 20% slower than target
            # Decrease quality to improve performance
            self.current_quality = max(
                self.min_quality,
                self.current_quality - self.adaptation_rate
            )
        elif avg_frame_time < self.target_frame_time * 0.8:  # 20% faster than target
            # Increase quality
            self.current_quality = min(
                self.max_quality,
                self.current_quality + self.adaptation_rate * 0.5  # Slower increase
            )
        
        if self.logger:
            self.logger.debug(f"Quality: {self.current_quality:.2f}, Frame time: {avg_frame_time*1000:.1f}ms")
        
        return self.current_quality
    
    def get_scaled_resolution(self, original_size: tuple) -> tuple:
        """Get scaled resolution based on current quality"""
        width, height = original_size
        scale = self.current_quality
        return (int(width * scale), int(height * scale))
