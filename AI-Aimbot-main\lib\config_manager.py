"""
Enhanced Configuration Manager for AI Aimbot
Provides advanced configuration management with validation and profiles
"""

import json
import logging
from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Dict, Any, Optional
from lib.aimbot import AimbotConfig, MouseMethod


class ConfigManager:
    """Advanced configuration manager with profiles and validation"""
    
    def __init__(self, config_dir: str = "lib/config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger("ConfigManager")
        
        # Configuration files
        self.sensitivity_file = self.config_dir / "config.json"
        self.aimbot_config_file = self.config_dir / "aimbot_config.json"
        self.profiles_file = self.config_dir / "profiles.json"
    
    def load_sensitivity_config(self) -> Dict[str, float]:
        """Load sensitivity configuration"""
        try:
            if self.sensitivity_file.exists():
                with open(self.sensitivity_file, 'r') as f:
                    config = json.load(f)
                    self._validate_sensitivity_config(config)
                    return config
            else:
                return self._get_default_sensitivity_config()
        except Exception as e:
            self.logger.error(f"Failed to load sensitivity config: {e}")
            return self._get_default_sensitivity_config()
    
    def save_sensitivity_config(self, xy_sens: float, targeting_sens: float) -> None:
        """Save sensitivity configuration"""
        try:
            config = {
                "xy_sens": xy_sens,
                "targeting_sens": targeting_sens,
                "xy_scale": 10.0 / xy_sens,
                "targeting_scale": 1000.0 / (targeting_sens * xy_sens)
            }
            
            self._validate_sensitivity_config(config)
            
            with open(self.sensitivity_file, 'w') as f:
                json.dump(config, f, indent=4)
            
            self.logger.info("Sensitivity configuration saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save sensitivity config: {e}")
            raise
    
    def load_aimbot_config(self) -> AimbotConfig:
        """Load aimbot configuration"""
        try:
            if self.aimbot_config_file.exists():
                with open(self.aimbot_config_file, 'r') as f:
                    config_dict = json.load(f)
                    
                # Convert mouse_method string to enum
                if 'mouse_method' in config_dict:
                    config_dict['mouse_method'] = MouseMethod(config_dict['mouse_method'])
                
                return AimbotConfig(**config_dict)
            else:
                return AimbotConfig()
        except Exception as e:
            self.logger.error(f"Failed to load aimbot config: {e}")
            return AimbotConfig()
    
    def save_aimbot_config(self, config: AimbotConfig) -> None:
        """Save aimbot configuration"""
        try:
            config_dict = asdict(config)
            
            # Convert enum to string for JSON serialization
            if 'mouse_method' in config_dict:
                config_dict['mouse_method'] = config_dict['mouse_method'].value
            
            with open(self.aimbot_config_file, 'w') as f:
                json.dump(config_dict, f, indent=4)
            
            self.logger.info("Aimbot configuration saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save aimbot config: {e}")
            raise
    
    def load_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Load configuration profiles"""
        try:
            if self.profiles_file.exists():
                with open(self.profiles_file, 'r') as f:
                    return json.load(f)
            else:
                return self._get_default_profiles()
        except Exception as e:
            self.logger.error(f"Failed to load profiles: {e}")
            return self._get_default_profiles()
    
    def save_profile(self, name: str, config: AimbotConfig, sensitivity: Dict[str, float]) -> None:
        """Save a configuration profile"""
        try:
            profiles = self.load_profiles()
            
            config_dict = asdict(config)
            if 'mouse_method' in config_dict:
                config_dict['mouse_method'] = config_dict['mouse_method'].value
            
            profiles[name] = {
                "aimbot_config": config_dict,
                "sensitivity_config": sensitivity,
                "description": f"Profile for {name}"
            }
            
            with open(self.profiles_file, 'w') as f:
                json.dump(profiles, f, indent=4)
            
            self.logger.info(f"Profile '{name}' saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save profile '{name}': {e}")
            raise
    
    def load_profile(self, name: str) -> Optional[tuple[AimbotConfig, Dict[str, float]]]:
        """Load a configuration profile"""
        try:
            profiles = self.load_profiles()
            
            if name not in profiles:
                self.logger.warning(f"Profile '{name}' not found")
                return None
            
            profile = profiles[name]
            
            # Load aimbot config
            aimbot_config_dict = profile["aimbot_config"]
            if 'mouse_method' in aimbot_config_dict:
                aimbot_config_dict['mouse_method'] = MouseMethod(aimbot_config_dict['mouse_method'])
            
            aimbot_config = AimbotConfig(**aimbot_config_dict)
            sensitivity_config = profile["sensitivity_config"]
            
            self.logger.info(f"Profile '{name}' loaded successfully")
            return aimbot_config, sensitivity_config
            
        except Exception as e:
            self.logger.error(f"Failed to load profile '{name}': {e}")
            return None
    
    def list_profiles(self) -> list[str]:
        """List available profiles"""
        profiles = self.load_profiles()
        return list(profiles.keys())
    
    def delete_profile(self, name: str) -> bool:
        """Delete a configuration profile"""
        try:
            profiles = self.load_profiles()
            
            if name not in profiles:
                self.logger.warning(f"Profile '{name}' not found")
                return False
            
            del profiles[name]
            
            with open(self.profiles_file, 'w') as f:
                json.dump(profiles, f, indent=4)
            
            self.logger.info(f"Profile '{name}' deleted successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete profile '{name}': {e}")
            return False
    
    def _validate_sensitivity_config(self, config: Dict[str, float]) -> None:
        """Validate sensitivity configuration"""
        required_keys = ["xy_sens", "targeting_sens", "xy_scale", "targeting_scale"]
        
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing required key: {key}")
            
            if not isinstance(config[key], (int, float)):
                raise ValueError(f"Invalid type for {key}: expected number")
            
            if config[key] <= 0:
                raise ValueError(f"Invalid value for {key}: must be positive")
    
    def _get_default_sensitivity_config(self) -> Dict[str, float]:
        """Get default sensitivity configuration"""
        return {
            "xy_sens": 1.0,
            "targeting_sens": 1.0,
            "xy_scale": 10.0,
            "targeting_scale": 1000.0
        }
    
    def _get_default_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get default configuration profiles"""
        return {
            "default": {
                "aimbot_config": asdict(AimbotConfig()),
                "sensitivity_config": self._get_default_sensitivity_config(),
                "description": "Default configuration profile"
            },
            "competitive": {
                "aimbot_config": {
                    **asdict(AimbotConfig()),
                    "confidence_threshold": 0.6,
                    "fov": 300,
                    "mouse_delay": 0.0005,
                    "enable_randomization": True,
                    "movement_randomness": 0.05,
                    "timing_randomness": 0.02
                },
                "sensitivity_config": self._get_default_sensitivity_config(),
                "description": "High-precision competitive gaming profile"
            },
            "casual": {
                "aimbot_config": {
                    **asdict(AimbotConfig()),
                    "confidence_threshold": 0.4,
                    "fov": 400,
                    "mouse_delay": 0.001,
                    "enable_randomization": True,
                    "movement_randomness": 0.15,
                    "timing_randomness": 0.08
                },
                "sensitivity_config": self._get_default_sensitivity_config(),
                "description": "Relaxed casual gaming profile"
            }
        }
