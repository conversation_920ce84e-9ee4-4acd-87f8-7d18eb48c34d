"""
Advanced Object Tracking Module
Implements Hungarian Algorithm-based tracking with GIOU metric
Based on NeuralBot's superior tracking system
"""

import math
import time
from dataclasses import dataclass
from typing import List, Optional, Tuple
import numpy as np
from scipy.optimize import linear_sum_assignment


@dataclass
class TrackedTarget:
    """Represents a tracked target with persistence"""
    id: int
    x1: int
    y1: int
    x2: int
    y2: int
    center_x: int
    center_y: int
    confidence: float
    last_seen: float
    tracking_time: float
    velocity_x: float = 0.0
    velocity_y: float = 0.0
    predicted_x: float = 0.0
    predicted_y: float = 0.0


class HungarianTracker:
    """Advanced object tracker using Hungarian Algorithm with GIOU metric"""
    
    def __init__(self, logger, max_inactive_time: float = 0.25, max_tracking_distance: int = 256):
        self.logger = logger
        self.max_inactive_time = max_inactive_time
        self.max_tracking_distance = max_tracking_distance
        
        # Tracking state
        self.active_targets: List[TrackedTarget] = []
        self.inactive_targets: List[TrackedTarget] = []
        self.next_id = 1
        
        # Performance tracking
        self.total_tracks = 0
        self.successful_tracks = 0
        
        self.logger.info("Hungarian tracker initialized")
    
    def track(self, detections: List[dict], current_time: float) -> List[TrackedTarget]:
        """
        Track objects using Hungarian Algorithm
        
        Args:
            detections: List of detection dictionaries with keys: x1, y1, x2, y2, confidence
            current_time: Current timestamp
            
        Returns:
            List of tracked targets
        """
        # Clean up old inactive targets
        self._cleanup_inactive_targets(current_time)
        
        if not detections:
            # No detections, age all active targets
            self._age_active_targets(current_time)
            return self.active_targets.copy()
        
        # Convert detections to target format
        detection_targets = []
        for det in detections:
            center_x = (det['x1'] + det['x2']) // 2
            center_y = (det['y1'] + det['y2']) // 2
            detection_targets.append({
                'x1': det['x1'], 'y1': det['y1'],
                'x2': det['x2'], 'y2': det['y2'],
                'center_x': center_x, 'center_y': center_y,
                'confidence': det.get('confidence', 0.5)
            })
        
        # Build cost matrix
        all_tracked = self.active_targets + self.inactive_targets
        cost_matrix = self._build_cost_matrix(detection_targets, all_tracked, current_time)
        
        # Solve assignment problem
        assignments = self._solve_assignment(cost_matrix, len(detection_targets), len(all_tracked))
        
        # Update tracking based on assignments
        new_active_targets = []
        used_detections = set()
        used_tracked = set()
        
        for det_idx, track_idx in assignments:
            if det_idx < len(detection_targets) and track_idx < len(all_tracked):
                detection = detection_targets[det_idx]
                tracked = all_tracked[track_idx]
                
                # Check distance constraint
                distance = self._calculate_distance(detection, tracked)
                if distance <= self.max_tracking_distance:
                    # Update existing track
                    updated_target = self._update_tracked_target(tracked, detection, current_time)
                    new_active_targets.append(updated_target)
                    used_detections.add(det_idx)
                    used_tracked.add(track_idx)
                    self.successful_tracks += 1
        
        # Handle unmatched detections (new targets)
        for det_idx, detection in enumerate(detection_targets):
            if det_idx not in used_detections:
                new_target = self._create_new_target(detection, current_time)
                new_active_targets.append(new_target)
                self.total_tracks += 1
        
        # Handle unmatched active targets (move to inactive)
        new_inactive_targets = []
        for track_idx, tracked in enumerate(all_tracked):
            if track_idx not in used_tracked:
                if tracked in self.active_targets:
                    # Move to inactive
                    tracked.last_seen = current_time
                    new_inactive_targets.append(tracked)
        
        # Update tracking lists
        self.active_targets = new_active_targets
        self.inactive_targets.extend(new_inactive_targets)
        
        return self.active_targets.copy()
    
    def _cleanup_inactive_targets(self, current_time: float) -> None:
        """Remove inactive targets that have exceeded the time limit"""
        self.inactive_targets = [
            target for target in self.inactive_targets
            if current_time - target.last_seen <= self.max_inactive_time
        ]
    
    def _age_active_targets(self, current_time: float) -> None:
        """Age active targets when no detections are available"""
        aged_targets = []
        for target in self.active_targets:
            if current_time - target.last_seen <= self.max_inactive_time:
                aged_targets.append(target)
            else:
                # Move to inactive
                target.last_seen = current_time
                self.inactive_targets.append(target)
        
        self.active_targets = aged_targets
    
    def _build_cost_matrix(self, detections: List[dict], tracked_targets: List[TrackedTarget], 
                          current_time: float) -> np.ndarray:
        """Build cost matrix using GIOU metric"""
        rows = len(detections)
        cols = len(tracked_targets)
        
        if rows == 0 or cols == 0:
            return np.array([])
        
        # Create cost matrix
        cost_matrix = np.zeros((rows, cols))
        
        for i, detection in enumerate(detections):
            for j, tracked in enumerate(tracked_targets):
                # Calculate GIOU cost (lower is better)
                giou_cost = self._calculate_giou_cost(detection, tracked)
                
                # Add temporal penalty for inactive targets
                if tracked in self.inactive_targets:
                    time_penalty = (current_time - tracked.last_seen) / self.max_inactive_time
                    giou_cost += time_penalty * 0.5
                
                cost_matrix[i, j] = giou_cost
        
        return cost_matrix
    
    def _calculate_giou_cost(self, detection: dict, tracked: TrackedTarget) -> float:
        """Calculate GIOU cost between detection and tracked target"""
        # Detection rectangle
        d_x1, d_y1, d_x2, d_y2 = detection['x1'], detection['y1'], detection['x2'], detection['y2']
        d_area = (d_x2 - d_x1) * (d_y2 - d_y1)
        
        # Tracked rectangle
        t_x1, t_y1, t_x2, t_y2 = tracked.x1, tracked.y1, tracked.x2, tracked.y2
        t_area = (t_x2 - t_x1) * (t_y2 - t_y1)
        
        # Handle degenerate cases
        if d_area <= 0 or t_area <= 0:
            return 1.0
        
        # Intersection
        xi1 = max(d_x1, t_x1)
        xi2 = min(d_x2, t_x2)
        yi1 = max(d_y1, t_y1)
        yi2 = min(d_y2, t_y2)
        
        intersection_area = 0.0
        if xi2 > xi1 and yi2 > yi1:
            intersection_area = (xi2 - xi1) * (yi2 - yi1)
        
        # Union
        union_area = d_area + t_area - intersection_area
        
        # IoU
        iou = intersection_area / union_area if union_area > 0 else 0.0
        
        # Smallest enclosing box
        xc1 = min(d_x1, t_x1)
        xc2 = max(d_x2, t_x2)
        yc1 = min(d_y1, t_y1)
        yc2 = max(d_y2, t_y2)
        enclosing_area = (xc2 - xc1) * (yc2 - yc1)
        
        # GIOU
        giou = iou - ((enclosing_area - union_area) / enclosing_area) if enclosing_area > 0 else 0.0
        
        # Return GIOU cost (1 - GIOU for minimization)
        return 1.0 - giou
    
    def _solve_assignment(self, cost_matrix: np.ndarray, num_detections: int, 
                         num_tracked: int) -> List[Tuple[int, int]]:
        """Solve assignment problem using Hungarian Algorithm"""
        if cost_matrix.size == 0:
            return []
        
        # Pad matrix to make it square if necessary
        max_dim = max(num_detections, num_tracked)
        if cost_matrix.shape[0] != max_dim or cost_matrix.shape[1] != max_dim:
            padded_matrix = np.full((max_dim, max_dim), 1.0)  # High cost for dummy assignments
            padded_matrix[:cost_matrix.shape[0], :cost_matrix.shape[1]] = cost_matrix
            cost_matrix = padded_matrix
        
        # Solve using scipy's implementation
        row_indices, col_indices = linear_sum_assignment(cost_matrix)
        
        # Filter out dummy assignments
        assignments = []
        for row, col in zip(row_indices, col_indices):
            if row < num_detections and col < num_tracked:
                assignments.append((row, col))
        
        return assignments
    
    def _calculate_distance(self, detection: dict, tracked: TrackedTarget) -> float:
        """Calculate Euclidean distance between detection and tracked target centers"""
        dx = detection['center_x'] - tracked.center_x
        dy = detection['center_y'] - tracked.center_y
        return math.sqrt(dx * dx + dy * dy)
    
    def _update_tracked_target(self, tracked: TrackedTarget, detection: dict, 
                              current_time: float) -> TrackedTarget:
        """Update existing tracked target with new detection"""
        dt = current_time - tracked.last_seen if tracked.last_seen > 0 else 0.0
        
        # Calculate velocity
        if dt > 0:
            velocity_x = (detection['center_x'] - tracked.center_x) / dt
            velocity_y = (detection['center_y'] - tracked.center_y) / dt
        else:
            velocity_x = tracked.velocity_x
            velocity_y = tracked.velocity_y
        
        # Smooth velocity with exponential moving average
        alpha = 0.3
        velocity_x = alpha * velocity_x + (1 - alpha) * tracked.velocity_x
        velocity_y = alpha * velocity_y + (1 - alpha) * tracked.velocity_y
        
        return TrackedTarget(
            id=tracked.id,
            x1=detection['x1'],
            y1=detection['y1'],
            x2=detection['x2'],
            y2=detection['y2'],
            center_x=detection['center_x'],
            center_y=detection['center_y'],
            confidence=detection['confidence'],
            last_seen=current_time,
            tracking_time=tracked.tracking_time + dt,
            velocity_x=velocity_x,
            velocity_y=velocity_y,
            predicted_x=detection['center_x'] + velocity_x * 0.1,  # Predict 100ms ahead
            predicted_y=detection['center_y'] + velocity_y * 0.1
        )
    
    def _create_new_target(self, detection: dict, current_time: float) -> TrackedTarget:
        """Create new tracked target from detection"""
        target_id = self.next_id
        self.next_id += 1
        
        return TrackedTarget(
            id=target_id,
            x1=detection['x1'],
            y1=detection['y1'],
            x2=detection['x2'],
            y2=detection['y2'],
            center_x=detection['center_x'],
            center_y=detection['center_y'],
            confidence=detection['confidence'],
            last_seen=current_time,
            tracking_time=0.0,
            velocity_x=0.0,
            velocity_y=0.0,
            predicted_x=detection['center_x'],
            predicted_y=detection['center_y']
        )
    
    def get_best_target(self, screen_center_x: int, screen_center_y: int) -> Optional[TrackedTarget]:
        """Get the best target based on distance to screen center and tracking quality"""
        if not self.active_targets:
            return None
        
        best_target = None
        best_score = float('inf')
        
        for target in self.active_targets:
            # Distance to center
            distance = math.sqrt(
                (target.center_x - screen_center_x) ** 2 + 
                (target.center_y - screen_center_y) ** 2
            )
            
            # Tracking quality (longer tracking time is better)
            tracking_quality = min(target.tracking_time, 2.0) / 2.0  # Normalize to 0-1
            
            # Confidence factor
            confidence_factor = target.confidence
            
            # Combined score (lower is better)
            score = distance / (1.0 + tracking_quality + confidence_factor)
            
            if score < best_score:
                best_score = score
                best_target = target
        
        return best_target
    
    def clear(self) -> None:
        """Clear all tracking data"""
        self.active_targets.clear()
        self.inactive_targets.clear()
        self.next_id = 1
        self.total_tracks = 0
        self.successful_tracks = 0
    
    def get_tracking_stats(self) -> dict:
        """Get tracking performance statistics"""
        success_rate = (self.successful_tracks / max(self.total_tracks, 1)) * 100
        
        return {
            'active_targets': len(self.active_targets),
            'inactive_targets': len(self.inactive_targets),
            'total_tracks': self.total_tracks,
            'successful_tracks': self.successful_tracks,
            'success_rate': success_rate,
            'next_id': self.next_id
        }
