#!/usr/bin/env python3
"""
Enhanced Lunar AI Aimbot - Improved Version
Features:
- Better error handling and logging
- Configuration profiles
- Performance optimizations
- Anti-detection features
- Modular architecture
"""

import argparse
import json
import os
import sys
import signal
import threading
from pathlib import Path
from pynput import keyboard
from termcolor import colored

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent / "lib"))

from aimbot import EnhancedAimbot, AimbotConfig, MouseMethod, Logger
from config_manager import ConfigManager


class EnhancedLunarApp:
    """Enhanced Lunar application with improved features"""
    
    def __init__(self):
        self.logger = Logger("LunarApp")
        self.config_manager = ConfigManager()
        self.aimbot = None
        self.keyboard_listener = None
        self.running = False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def on_key_release(self, key):
        """Handle keyboard events"""
        try:
            if key == keyboard.Key.f1:
                if self.aimbot:
                    self.aimbot.toggle_state()
            elif key == keyboard.Key.f2:
                self.shutdown()
            elif key == keyboard.Key.f3:
                self.show_stats()
        except Exception as e:
            self.logger.error(f"Keyboard handler error: {e}")
    
    def show_stats(self):
        """Display performance statistics"""
        if self.aimbot:
            runtime = time.time() - self.aimbot.start_time
            avg_fps = self.aimbot.frame_count / runtime if runtime > 0 else 0
            print(f"\n[STATS] Frames: {self.aimbot.frame_count}, "
                  f"Runtime: {runtime:.1f}s, Avg FPS: {avg_fps:.1f}")
    
    def setup_sensitivity(self):
        """Interactive sensitivity setup"""
        print(colored("\n=== SENSITIVITY CONFIGURATION ===", "cyan"))
        print("[INFO] In-game X and Y axis sensitivity should be the same")
        print("[INFO] Your targeting sensitivity must match your scoping sensitivity")
        
        def get_float_input(prompt: str) -> float:
            while True:
                try:
                    value = float(input(prompt))
                    if value <= 0:
                        print("[!] Value must be positive")
                        continue
                    return value
                except ValueError:
                    print("[!] Invalid input. Enter a number (e.g., 6.9)")
        
        xy_sens = get_float_input("X-Axis and Y-Axis Sensitivity: ")
        targeting_sens = get_float_input("Targeting Sensitivity: ")
        
        try:
            self.config_manager.save_sensitivity_config(xy_sens, targeting_sens)
            print(colored("[INFO] Sensitivity configuration saved!", "green"))
        except Exception as e:
            print(colored(f"[ERROR] Failed to save configuration: {e}", "red"))
            return False
        
        return True
    
    def setup_aimbot_config(self):
        """Interactive aimbot configuration"""
        print(colored("\n=== AIMBOT CONFIGURATION ===", "cyan"))
        
        config = self.config_manager.load_aimbot_config()
        
        print(f"Current FOV: {config.fov}")
        print(f"Current Confidence: {config.confidence_threshold}")
        print(f"Current Mouse Method: {config.mouse_method.value}")
        
        if input("Modify configuration? (y/N): ").lower().startswith('y'):
            try:
                config.fov = int(input(f"FOV ({config.fov}): ") or config.fov)
                config.confidence_threshold = float(input(f"Confidence ({config.confidence_threshold}): ") or config.confidence_threshold)
                
                mouse_method = input(f"Mouse method (win32/ddxoft) ({config.mouse_method.value}): ") or config.mouse_method.value
                config.mouse_method = MouseMethod(mouse_method.lower())
                
                self.config_manager.save_aimbot_config(config)
                print(colored("[INFO] Aimbot configuration saved!", "green"))
                
            except Exception as e:
                print(colored(f"[ERROR] Configuration error: {e}", "red"))
                return False
        
        return True
    
    def list_profiles(self):
        """List available configuration profiles"""
        profiles = self.config_manager.list_profiles()
        if profiles:
            print(colored("\n=== AVAILABLE PROFILES ===", "cyan"))
            for i, profile in enumerate(profiles, 1):
                print(f"{i}. {profile}")
        else:
            print(colored("[INFO] No profiles available", "yellow"))
    
    def load_profile(self, profile_name: str = None):
        """Load a configuration profile"""
        if not profile_name:
            self.list_profiles()
            profile_name = input("\nEnter profile name: ").strip()
        
        if not profile_name:
            return False
        
        result = self.config_manager.load_profile(profile_name)
        if result:
            config, sensitivity = result
            self.config_manager.save_aimbot_config(config)
            # Note: Sensitivity config is loaded separately
            print(colored(f"[INFO] Profile '{profile_name}' loaded successfully!", "green"))
            return True
        else:
            print(colored(f"[ERROR] Failed to load profile '{profile_name}'", "red"))
            return False
    
    def save_profile(self):
        """Save current configuration as a profile"""
        profile_name = input("Enter profile name: ").strip()
        if not profile_name:
            print(colored("[ERROR] Profile name cannot be empty", "red"))
            return False
        
        try:
            config = self.config_manager.load_aimbot_config()
            sensitivity = self.config_manager.load_sensitivity_config()
            self.config_manager.save_profile(profile_name, config, sensitivity)
            print(colored(f"[INFO] Profile '{profile_name}' saved successfully!", "green"))
            return True
        except Exception as e:
            print(colored(f"[ERROR] Failed to save profile: {e}", "red"))
            return False
    
    def show_banner(self):
        """Display application banner"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        banner = colored('''
  _    _   _ _   _    _    ____     _     ___ _____ _____     _____ _   _ _   _    _    _   _  ____ _____ ____  
 | |  | | | | \ | |  / \  |  _ \   | |   |_ _|_   _| ____|   | ____| \ | | | | |  / \  | \ | |/ ___| ____|  _ \ 
 | |  | | | |  \| | / _ \ | |_) |  | |    | |  | | |  _|     |  _| |  \| | |_| | / _ \ |  \| | |   |  _| | | | |
 | |__| |_| | |\  |/ ___ \|  _ <   | |___ | |  | | | |___    | |___| |\  |  _  |/ ___ \| |\  | |___| |___| |_| |
 |_____\___/|_| \_/_/   \_\_| \_\  |_____|___| |_| |_____|   |_____|_| \_|_| |_/_/   \_\_| \_|\____|_____|____/ 
                                                                                                                
(Enhanced Neural Network Aimbot)''', "green")
        
        print(banner)
        print(colored('Enhanced version with improved features and performance', "cyan"))
        print(colored('Original Lunar by zeyad-mansour | Enhanced by AI Assistant', "yellow"))
        print(colored('For premium features visit: https://gannonr.com/lunar', "red"))
    
    def run(self, args):
        """Main application entry point"""
        try:
            self.setup_signal_handlers()
            self.show_banner()
            
            # Handle command line arguments
            if args.setup:
                return self.setup_sensitivity() and self.setup_aimbot_config()
            
            if args.list_profiles:
                self.list_profiles()
                return True
            
            if args.load_profile:
                return self.load_profile(args.load_profile)
            
            if args.save_profile:
                return self.save_profile()
            
            # Check if sensitivity is configured
            if not self.config_manager.sensitivity_file.exists():
                print(colored("[!] Sensitivity configuration not found", "yellow"))
                if not self.setup_sensitivity():
                    return False
            
            # Load configuration
            aimbot_config = self.config_manager.load_aimbot_config()
            
            # Create data directory if collecting data
            if args.collect_data:
                Path("lib/data").mkdir(exist_ok=True)
            
            # Initialize aimbot
            self.aimbot = EnhancedAimbot(aimbot_config, args.collect_data)
            
            # Start keyboard listener
            self.keyboard_listener = keyboard.Listener(on_release=self.on_key_release)
            self.keyboard_listener.start()
            
            print(colored("\n[CONTROLS]", "cyan"))
            print("F1 - Toggle Aimbot")
            print("F2 - Quit")
            print("F3 - Show Stats")
            print(colored("\nStarting aimbot...", "green"))
            
            # Start aimbot
            self.running = True
            self.aimbot.start()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            return True
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            return False
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        if not self.running:
            return
        
        self.running = False
        self.logger.info("Shutting down application...")
        
        try:
            if self.aimbot:
                self.aimbot.stop()
            
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            
            # Small delay to allow cleanup
            import time
            time.sleep(0.5)
            
        except Exception as e:
            self.logger.error(f"Shutdown error: {e}")
        
        print(colored("\n[INFO] Application shutdown complete", "green"))
        os._exit(0)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced Lunar AI Aimbot")
    parser.add_argument("--setup", action="store_true", help="Run configuration setup")
    parser.add_argument("--collect-data", action="store_true", help="Enable data collection mode")
    parser.add_argument("--list-profiles", action="store_true", help="List available profiles")
    parser.add_argument("--load-profile", type=str, help="Load a configuration profile")
    parser.add_argument("--save-profile", action="store_true", help="Save current config as profile")
    
    args = parser.parse_args()
    
    # Hide pygame support prompt
    os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
    
    app = EnhancedLunarApp()
    success = app.run(args)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
