#!/usr/bin/env python3
"""
Lunar AI Aimbot - NeuralBot Enhanced Edition
Combines the best of both worlds with Xbox controller support

Features from NeuralBot integration:
- Hungarian Algorithm object tracking
- Multiple advanced aiming methods (Flick, PID, Hybrid, Predictive)
- ESP overlay system with visual debugging
- GIOU-based target matching
- Xbox controller support for console gaming

Usage:
    python lunar_neuralbot_enhanced.py --controller
    python lunar_neuralbot_enhanced.py --setup-controller
    python lunar_neuralbot_enhanced.py --aiming-method hybrid
"""

import argparse
import os
import sys
import time
import threading
from pathlib import Path
from pynput import keyboard
from termcolor import colored

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent / "lib"))

try:
    from aimbot import EnhancedAimbot, AimbotConfig, InputMethod, Logger
    from config_manager import ConfigManager
    from xbox_controller import XboxControllerManager, ControllerConfig
    from advanced_tracking import HungarianTracker
    from advanced_aimers import AdvancedAimingSystem, AimingConfig, <PERSON><PERSON><PERSON><PERSON><PERSON>
    from esp_overlay import ESPOverlay, OverlayConfig, OverlayMode
    from performance_optimizer import PerformanceOptimizer
    from anti_detection import AntiDetectionSystem, HumanBehaviorProfile
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure all required dependencies are installed:")
    print("pip install pygame xinput-python inputs scipy")
    sys.exit(1)


class NeuralBotEnhancedApp:
    """Enhanced Lunar application with NeuralBot features and Xbox controller support"""
    
    def __init__(self):
        self.logger = Logger("NeuralBotEnhanced")
        self.config_manager = ConfigManager()
        
        # Core components
        self.aimbot = None
        self.controller_manager = None
        self.tracker = None
        self.aiming_system = None
        self.esp_overlay = None
        self.performance_optimizer = None
        self.anti_detection = None
        
        # State
        self.running = False
        self.keyboard_listener = None
        self.aimbot_enabled = True
        
        # Performance tracking
        self.frame_count = 0
        self.start_time = time.time()
        
        self.logger.info("NeuralBot Enhanced application initialized")
    
    def setup_xbox_controller(self) -> bool:
        """Setup Xbox controller configuration"""
        print(colored("\n=== XBOX CONTROLLER SETUP ===", "cyan"))
        print("Make sure your Xbox controller is connected via Bluetooth")
        
        # Test controller connection
        try:
            controller_config = ControllerConfig()
            test_controller = XboxControllerManager(controller_config, self.logger)
            
            if not test_controller.initialize_controller():
                print(colored("[ERROR] No Xbox controller detected!", "red"))
                print("Please ensure your controller is:")
                print("1. Properly paired via Bluetooth")
                print("2. Turned on and connected")
                print("3. Recognized by Windows")
                return False
            
            print(colored("[SUCCESS] Xbox controller detected!", "green"))
            
            # Interactive configuration
            print("\n[INFO] Configure controller settings:")
            
            try:
                sensitivity = float(input(f"Aim sensitivity (0.1-2.0, default {controller_config.aim_sensitivity}): ") 
                                 or controller_config.aim_sensitivity)
                controller_config.aim_sensitivity = max(0.1, min(2.0, sensitivity))
                
                deadzone = float(input(f"Right stick deadzone (0.05-0.3, default {controller_config.right_stick_deadzone}): ") 
                               or controller_config.right_stick_deadzone)
                controller_config.right_stick_deadzone = max(0.05, min(0.3, deadzone))
                
                # Aiming method selection
                print("\nAvailable aiming methods:")
                for i, method in enumerate(AimingMethod, 1):
                    print(f"{i}. {method.value.title()}")
                
                method_choice = input("Select aiming method (1-4, default 3 for Hybrid): ") or "3"
                method_index = max(1, min(4, int(method_choice))) - 1
                selected_method = list(AimingMethod)[method_index]
                
                # Save controller configuration
                controller_data = {
                    "aim_sensitivity": controller_config.aim_sensitivity,
                    "right_stick_deadzone": controller_config.right_stick_deadzone,
                    "aiming_method": selected_method.value,
                    "enable_acceleration": True,
                    "aim_assist_strength": 0.8
                }
                
                config_file = Path("lib/config/controller_config.json")
                config_file.parent.mkdir(exist_ok=True)
                
                import json
                with open(config_file, 'w') as f:
                    json.dump(controller_data, f, indent=4)
                
                print(colored("[SUCCESS] Controller configuration saved!", "green"))
                return True
                
            except ValueError:
                print(colored("[ERROR] Invalid input values", "red"))
                return False
            
        except Exception as e:
            print(colored(f"[ERROR] Controller setup failed: {e}", "red"))
            return False
    
    def load_controller_config(self) -> tuple:
        """Load controller configuration"""
        config_file = Path("lib/config/controller_config.json")
        
        if not config_file.exists():
            # Use defaults
            controller_config = ControllerConfig()
            aiming_method = AimingMethod.HYBRID
        else:
            try:
                import json
                with open(config_file, 'r') as f:
                    data = json.load(f)
                
                controller_config = ControllerConfig(
                    aim_sensitivity=data.get("aim_sensitivity", 1.0),
                    right_stick_deadzone=data.get("right_stick_deadzone", 0.15),
                    enable_acceleration=data.get("enable_acceleration", True),
                    aim_assist_strength=data.get("aim_assist_strength", 0.8)
                )
                
                aiming_method = AimingMethod(data.get("aiming_method", "hybrid"))
                
            except Exception as e:
                self.logger.error(f"Failed to load controller config: {e}")
                controller_config = ControllerConfig()
                aiming_method = AimingMethod.HYBRID
        
        return controller_config, aiming_method
    
    def initialize_components(self, use_controller: bool = True, aiming_method: str = "hybrid") -> bool:
        """Initialize all system components"""
        try:
            # Load configurations
            aimbot_config = self.config_manager.load_aimbot_config()
            aimbot_config.input_method = InputMethod.XBOX_CONTROLLER if use_controller else InputMethod.MOUSE
            
            # Initialize Xbox controller if requested
            if use_controller:
                controller_config, selected_aiming_method = self.load_controller_config()
                
                # Override aiming method if specified
                if aiming_method != "hybrid":
                    try:
                        selected_aiming_method = AimingMethod(aiming_method.lower())
                    except ValueError:
                        self.logger.warning(f"Invalid aiming method: {aiming_method}, using hybrid")
                        selected_aiming_method = AimingMethod.HYBRID
                
                self.controller_manager = XboxControllerManager(controller_config, self.logger)
                if not self.controller_manager.start_input_loop():
                    self.logger.error("Failed to initialize Xbox controller")
                    return False
                
                # Initialize advanced aiming system
                aiming_config = AimingConfig(
                    method=selected_aiming_method,
                    max_speed=1.0,
                    sensitivity=controller_config.aim_sensitivity,
                    flick_speed=4000.0,
                    pid_kp=2.0,
                    hybrid_switch_distance=50.0,
                    prediction_time=0.1
                )
                
                self.aiming_system = AdvancedAimingSystem(aiming_config, self.logger)
                self.logger.info(f"Initialized aiming system with method: {selected_aiming_method.value}")
            
            # Initialize Hungarian tracker
            self.tracker = HungarianTracker(self.logger, max_inactive_time=0.25)
            
            # Initialize ESP overlay
            overlay_config = OverlayConfig(
                mode=OverlayMode.STANDARD,
                show_controller_input=use_controller
            )
            self.esp_overlay = ESPOverlay(overlay_config, self.logger)
            
            # Initialize performance optimizer
            self.performance_optimizer = PerformanceOptimizer(self.logger)
            self.performance_optimizer.start_monitoring()
            
            # Initialize anti-detection system
            behavior_profile = HumanBehaviorProfile(
                reaction_time_min=0.15,
                reaction_time_max=0.35,
                accuracy_variance=0.1,
                movement_smoothness=0.8
            )
            self.anti_detection = AntiDetectionSystem(self.logger, behavior_profile)
            
            # Initialize enhanced aimbot
            self.aimbot = EnhancedAimbot(aimbot_config, collect_data=False)
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Component initialization failed: {e}")
            return False
    
    def controller_input_callback(self, x_input: float, y_input: float) -> None:
        """Callback for controller input injection"""
        # This would inject actual controller input in a real implementation
        # For now, we'll log the input for debugging
        if abs(x_input) > 0.01 or abs(y_input) > 0.01:
            self.logger.debug(f"Controller input: X={x_input:.3f}, Y={y_input:.3f}")
    
    def on_key_release(self, key):
        """Handle keyboard events"""
        try:
            if key == keyboard.Key.f1:
                self.aimbot_enabled = not self.aimbot_enabled
                status = "ENABLED" if self.aimbot_enabled else "DISABLED"
                print(f"\r[!] AIMBOT IS [{colored(status, 'green' if self.aimbot_enabled else 'red')}]", end="")
            elif key == keyboard.Key.f2:
                self.shutdown()
            elif key == keyboard.Key.f3:
                self.show_performance_stats()
            elif key == keyboard.Key.f4:
                # Toggle ESP overlay mode
                if self.esp_overlay:
                    modes = list(OverlayMode)
                    current_index = modes.index(self.esp_overlay.config.mode)
                    next_mode = modes[(current_index + 1) % len(modes)]
                    self.esp_overlay.set_mode(next_mode)
                    print(f"\n[INFO] ESP mode: {next_mode.value}")
            elif key == keyboard.Key.f5:
                # Switch aiming method
                if self.aiming_system:
                    methods = list(AimingMethod)
                    current_index = methods.index(self.aiming_system.config.method)
                    next_method = methods[(current_index + 1) % len(methods)]
                    self.aiming_system.set_aiming_method(next_method)
                    print(f"\n[INFO] Aiming method: {next_method.value}")
        except Exception as e:
            self.logger.error(f"Keyboard handler error: {e}")
    
    def show_performance_stats(self):
        """Display comprehensive performance statistics"""
        if self.performance_optimizer:
            print(self.performance_optimizer.get_performance_report())
        
        if self.tracker:
            stats = self.tracker.get_tracking_stats()
            print(f"\n=== TRACKING STATS ===")
            print(f"Active Targets: {stats['active_targets']}")
            print(f"Success Rate: {stats['success_rate']:.1f}%")
        
        if self.anti_detection:
            print(self.anti_detection.get_humanization_report())
    
    def show_banner(self):
        """Display enhanced application banner"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        banner = colored('''
 ███▄    █ ▓█████  █    ██  ██▀███   ▄▄▄       ██▓     ▄▄▄▄    ▒█████  ▄▄▄█████▓
 ██ ▀█   █ ▓█   ▀  ██  ▓██▒▓██ ▒ ██▒▒████▄    ▓██▒    ▓█████▄ ▒██▒  ██▒▓  ██▒ ▓▒
▓██  ▀█ ██▒▒███   ▓██  ▒██░▓██ ░▄█ ▒▒██  ▀█▄  ▒██░    ▒██▒ ▄██▒██░  ██▒▒ ▓██░ ▒░
▓██▒  ▐▌██▒▒▓█  ▄ ▓▓█  ░██░▒██▀▀█▄  ░██▄▄▄▄██ ▒██░    ▒██░█▀  ▒██   ██░░ ▓██▓ ░ 
▒██░   ▓██░░▒████▒▒▒█████▓ ░██▓ ▒██▒ ▓█   ▓██▒░██████▒░▓█  ▀█▓░ ████▓▒░  ▒██▒ ░ 
░ ▒░   ▒ ▒ ░░ ▒░ ░░▒▓▒ ▒ ▒ ░ ▒▓ ░▒▓░ ▒▒   ▓▒█░░ ▒░▓  ░░▒▓███▀▒░ ▒░▒░▒░   ▒ ░░   
░ ░░   ░ ▒░ ░ ░  ░░░▒░ ░ ░   ░▒ ░ ▒░  ▒   ▒▒ ░░ ░ ▒  ░▒░▒   ░   ░ ▒ ▒░     ░    
   ░   ░ ░    ░    ░░░ ░ ░   ░░   ░   ░   ▒     ░ ░    ░    ░ ░ ░ ░ ▒    ░      
         ░    ░  ░   ░        ░           ░  ░    ░  ░ ░          ░ ░           
                                                       ░                        
        ENHANCED WITH NEURALBOT FEATURES + XBOX CONTROLLER SUPPORT''', "green")
        
        print(banner)
        print(colored('Advanced AI Aimbot with Hungarian Tracking & Multiple Aiming Methods', "cyan"))
        print(colored('Original Lunar by zeyad-mansour | NeuralBot by AccessViolationEnjoyer', "yellow"))
        print(colored('Enhanced Integration by AI Assistant', "magenta"))
    
    def run(self, args):
        """Main application entry point"""
        try:
            self.show_banner()
            
            # Handle setup mode
            if args.setup_controller:
                return self.setup_xbox_controller()
            
            # Initialize components
            if not self.initialize_components(args.controller, args.aiming_method):
                return False
            
            # Start keyboard listener
            self.keyboard_listener = keyboard.Listener(on_release=self.on_key_release)
            self.keyboard_listener.start()
            
            print(colored("\n[CONTROLS]", "cyan"))
            print("F1 - Toggle Aimbot")
            print("F2 - Quit")
            print("F3 - Show Performance Stats")
            print("F4 - Toggle ESP Overlay Mode")
            if args.controller:
                print("F5 - Switch Aiming Method")
                print("Controller: Left Trigger = Aim, Right Trigger = Shoot")
            
            print(colored(f"\nStarting enhanced aimbot with {'Xbox Controller' if args.controller else 'Mouse'}...", "green"))
            
            # Main loop would go here
            self.running = True
            self.main_loop()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            return True
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            return False
        finally:
            self.shutdown()
    
    def main_loop(self):
        """Main application loop with enhanced features"""
        # This is a simplified version - the full implementation would integrate
        # all the components in the actual aimbot loop
        
        print(colored("[INFO] Enhanced aimbot is running!", "green"))
        print("Press F2 to quit...")
        
        try:
            while self.running:
                time.sleep(0.1)  # Prevent busy waiting
                
                # Update performance metrics
                self.frame_count += 1
                
                # Check controller status
                if self.controller_manager and not self.controller_manager.state.connected:
                    self.logger.warning("Xbox controller disconnected!")
                
        except KeyboardInterrupt:
            pass
    
    def shutdown(self):
        """Graceful shutdown with cleanup"""
        if not self.running:
            return
        
        self.running = False
        self.logger.info("Shutting down enhanced application...")
        
        try:
            # Stop components
            if self.controller_manager:
                self.controller_manager.stop_input_loop()
            
            if self.performance_optimizer:
                self.performance_optimizer.stop_monitoring()
            
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            
            # Show final stats
            if self.frame_count > 0:
                runtime = time.time() - self.start_time
                print(f"\n[STATS] Runtime: {runtime:.1f}s, Frames: {self.frame_count}")
            
        except Exception as e:
            self.logger.error(f"Shutdown error: {e}")
        
        print(colored("\n[INFO] Enhanced application shutdown complete", "green"))
        os._exit(0)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Lunar AI Aimbot - NeuralBot Enhanced Edition")
    parser.add_argument("--controller", action="store_true", help="Use Xbox controller input")
    parser.add_argument("--setup-controller", action="store_true", help="Setup Xbox controller configuration")
    parser.add_argument("--aiming-method", choices=["flick", "pid_feedback", "hybrid", "predictive"], 
                       default="hybrid", help="Select aiming method")
    parser.add_argument("--overlay-mode", choices=["minimal", "standard", "debug", "full"], 
                       default="standard", help="ESP overlay mode")
    
    args = parser.parse_args()
    
    # Hide pygame support prompt
    os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
    
    app = NeuralBotEnhancedApp()
    success = app.run(args)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
